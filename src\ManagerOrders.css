/* ========================================= */
/* Enhanced Manager Orders Styles */
/* ========================================= */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #6d4c41;
  --primary-light: #8d6e63;
  --primary-dark: #5d4037;
  --accent-color: #ffab40;
  --accent-dark: #ff9800;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --info-color: #2196f3;
  --light-bg: #f5f7fa;
  --white: #ffffff;
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ========================================= */
/* Manager Orders Container */
/* ========================================= */

.manager-orders-container {
  font-family: var(--font-family);
  animation: fadeIn 0.6s ease-out;
}

.manager-orders-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(109, 76, 65, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 171, 64, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* ========================================= */
/* Enhanced Header Styles */
/* ========================================= */

.manager-orders-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.manager-orders-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
  pointer-events: none;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

.stat-card i {
  font-size: 1.5rem;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
  display: block;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* ========================================= */
/* Enhanced Discount Requests Section */
/* ========================================= */

.discount-requests-section {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border: 2px solid rgba(255, 152, 0, 0.3);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.15);
}

.discount-requests-section::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  background: rgba(255, 152, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.discount-request-card {
  background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
  border: 2px solid rgba(255, 152, 0, 0.2);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.discount-request-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--warning-color), #ffc107);
}

.discount-request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
  border-color: var(--warning-color);
}

.request-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(135deg, var(--warning-color), #ffc107);
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 600;
  animation: pulse 2s ease-in-out infinite;
}

/* ========================================= */
/* Enhanced Modal Styles */
/* ========================================= */

.enhanced-modal-overlay {
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  animation: fadeIn 0.3s ease-out !important;
}

.enhanced-modal {
  animation: slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
  transform-origin: center bottom;
}

.enhanced-modal::-webkit-scrollbar {
  width: 8px;
}

.enhanced-modal::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.enhanced-modal::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 10px;
}

.enhanced-modal::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* ========================================= */
/* Enhanced Button Styles */
/* ========================================= */

.enhanced-btn {
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  border: none;
  border-radius: var(--border-radius);
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.enhanced-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.enhanced-btn:hover::before {
  left: 100%;
}

.enhanced-btn:hover {
  transform: translateY(-2px);
}

.enhanced-btn.success {
  background: linear-gradient(135deg, var(--success-color) 0%, #66bb6a 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.enhanced-btn.success:hover {
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.enhanced-btn.danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #e57373 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.enhanced-btn.danger:hover {
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.enhanced-btn.secondary {
  background: linear-gradient(135deg, #757575 0%, #9e9e9e 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(117, 117, 117, 0.3);
}

.enhanced-btn.secondary:hover {
  box-shadow: 0 6px 20px rgba(117, 117, 117, 0.4);
}

.enhanced-btn:disabled {
  background: #ccc !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.enhanced-btn:disabled::before {
  display: none;
}

/* ========================================= */
/* Enhanced Form Elements */
/* ========================================= */

.enhanced-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(156, 39, 176, 0.2);
  border-radius: var(--border-radius);
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.8);
  transition: var(--transition);
  outline: none;
}

.enhanced-textarea:focus {
  border-color: #9c27b0;
  box-shadow: 0 0 0 3px rgba(156, 39, 176, 0.1);
}

/* ========================================= */
/* Responsive Design */
/* ========================================= */

@media (max-width: 768px) {
  .manager-orders-container {
    padding: 1rem;
  }

  .manager-orders-header {
    padding: 1.5rem;
    border-radius: var(--border-radius);
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .discount-requests-section {
    padding: 1.5rem;
    border-radius: var(--border-radius);
  }

  .discount-request-card {
    padding: 1rem;
  }

  .enhanced-modal {
    width: 98% !important;
    margin: 1% !important;
    border-radius: var(--border-radius) !important;
  }

  .enhanced-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .manager-orders-header {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .enhanced-modal {
    width: 100% !important;
    height: 100vh !important;
    max-height: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }

  .enhanced-btn {
    padding: 1rem;
    font-size: 0.85rem;
  }
}

/* ========================================= */
/* Touch Interactions */
/* ========================================= */

@media (hover: none) and (pointer: coarse) {
  .enhanced-btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .discount-request-card:active {
    transform: scale(0.99);
    transition: transform 0.1s ease;
  }
}

/* ========================================= */
/* Print Styles */
/* ========================================= */

@media print {
  .enhanced-modal-overlay {
    position: static !important;
    background: var(--white) !important;
    backdrop-filter: none !important;
  }

  .enhanced-modal {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    max-width: 100% !important;
    width: 100% !important;
    max-height: none !important;
    overflow: visible !important;
    animation: none !important;
  }

  .enhanced-btn {
    display: none !important;
  }

  .manager-orders-header {
    background: #f8f9fa !important;
    color: #000 !important;
  }
}
