import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { loginUser } from './utils/api';
import './LoginPage.css';
import ThemeToggle from './components/ThemeToggle';
import Button from './components/Button';
import { ToastContainer } from './components/Toast';
import { useToast } from './hooks/useToast';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toasts, removeToast, showError, showSuccess } = useToast();
  // إعادة التوجيه إذا كان المستخدم مسجلاً بالفعل
  useEffect(() => {
    try {
      const userJson = localStorage.getItem('user');
      if (!userJson) return;

      // تحقق من صحة تنسيق JSON قبل التحليل
      if (typeof userJson !== 'string') {
        localStorage.removeItem('user');
        return;
      }

      const user = JSON.parse(userJson);

      // تحقق من صحة بيانات المستخدم
      if (!user || typeof user !== 'object' || !user.role || typeof user.role !== 'string') {
        localStorage.removeItem('user');
        return;
      }

      // تحويل الأدوار من الإنجليزية إلى العربية
      const roleMapping: Record<string, string> = {
        'admin': 'مدير',
        'manager': 'مدير',
        'employee': 'نادل',
        'waiter': 'نادل',
        'chef': 'طباخ'
      };

      // تحويل الدور إلى العربية
      const arabicRole = roleMapping[user.role] || user.role;

      // التحقق من أن الدور صالح
      const validRoles = ['نادل', 'طباخ', 'مدير'];
      if (!validRoles.includes(arabicRole)) {
        localStorage.removeItem('user');
        return;
      }

      // تحديث دور المستخدم بالعربية
      user.role = arabicRole;
      localStorage.setItem('user', JSON.stringify(user));

      // إعادة التوجيه بناءً على الدور
      const roleRedirects: Record<string, string> = {
        'نادل': '/waiter',
        'طباخ': '/chef',
        'مدير': '/manager'
      };

      const redirectPath = roleRedirects[user.role];
      if (redirectPath) {
        navigate(redirectPath, { replace: true });
      } else {
        localStorage.removeItem('user');
      }
    } catch (error) {
      // في حالة حدوث أي خطأ، نقوم بمسح التخزين المحلي
      console.error('خطأ في قراءة بيانات المستخدم:', error);
      localStorage.removeItem('user');
    }
  }, [navigate]);
  // تسجيل الدخول عبر API
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // تنظيف المدخلات
      const trimmedUsername = username.trim();
      const trimmedPassword = password.trim();

      // التحقق من المدخلات
      if (!trimmedUsername || !trimmedPassword) {
        showError('الرجاء إدخال اسم المستخدم وكلمة المرور');
        return;
      }

      // إرسال طلب تسجيل الدخول بدون تحديد الدور
      const response = await loginUser({
        username: trimmedUsername,
        password: trimmedPassword
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'فشل في تسجيل الدخول');
      }

      const data = response.data;

      // التحقق من صحة بيانات الاستجابة
      if (!data.user || typeof data.user !== 'object' || !data.user.role) {
        throw new Error('استجابة غير صالحة من الخادم');
      }

      // تطبيع الدور إلى الإنجليزية للاستخدام الداخلي
      const normalizeRole = (role: string): string => {
        const roleMapping: Record<string, string> = {
          'admin': 'manager',
          'manager': 'manager',
          'employee': 'waiter',
          'waiter': 'waiter',
          'chef': 'chef'
        };
        return roleMapping[role] || role;
      };

      const normalizedRole = normalizeRole(data.user.role);

      // تخزين بيانات المستخدم مع الدور بالإنجليزية للتوافق مع النظام
      const safeUserData = {
        id: data.user.id,
        username: data.user.name,
        name: data.user.name,
        role: normalizedRole // استخدام الدور بالإنجليزية
      };

      localStorage.setItem('user', JSON.stringify(safeUserData));
      localStorage.setItem('username', data.user.name);
      localStorage.setItem('waiterName', data.user.name);
      localStorage.setItem('waiterId', data.user.id);
      localStorage.setItem('userRole', normalizedRole);
      localStorage.setItem('token', data.token);

      const roleRedirects: { [key: string]: string } = {
        'waiter': '/waiter',
        'chef': '/chef',
        'manager': '/manager'
      };

      const redirectPath = roleRedirects[normalizedRole];
      if (!redirectPath) {
        throw new Error(`دور غير صالح: ${normalizedRole}`);
      }

      showSuccess('تم تسجيل الدخول بنجاح');
      navigate(redirectPath, { replace: true });

    } catch (error: any) {
      console.error('خطأ في تسجيل الدخول:', error);
      showError(error.message || 'حدث خطأ في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="theme-toggle-container">
        <ThemeToggle />
      </div>

      <div className="login-wrapper">
        <img src="/coffee-logo.svg" alt="شعار المقهى" className="app-logo" />

        {/* عرض اسم المستخدم الحالي وصلاحيته إذا كان مسجلاً */}
        {localStorage.getItem('user') && (() => {
          try {
            // تم حذف عرض اسم المستخدم الحالي وصلاحيته نهائياً بناءً على طلب المستخدم
            return null;
          } catch (error) {
            console.log('Error parsing user data:', error);
          }
          return null;
        })()}

        <form className="login-form" onSubmit={handleSubmit}>
          <h2>تسجيل الدخول</h2>

          <label>
            <i className="fas fa-user input-icon"></i>
            اسم المستخدم
            <input
              type="text"
              value={username}
              onChange={e => setUsername(e.target.value)}
              required
              placeholder="أدخل اسم المستخدم"
              disabled={isLoading}
            />
          </label>

          <label>
            <i className="fas fa-lock input-icon"></i>
            كلمة المرور
            <input
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              placeholder="••••••••"
              disabled={isLoading}
            />
          </label>



          <Button
            type="submit"
            disabled={isLoading}
            loading={isLoading}
            fullWidth
            icon="fas fa-sign-in-alt"
            iconPosition="start"
            variant="primary"
            size="lg"
          >
            {isLoading ? 'جاري الدخول...' : 'دخول'}
          </Button>

          <div className="login-help">
            {/* تم حذف بيانات الدخول التجريبية نهائياً */}
          </div>
        </form>
      </div>

      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
