import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import socket from './socket';
import { useToast } from './hooks/useToast';
import { useNotifications } from './hooks/useNotifications';
import { ToastContainer } from './components/Toast';
import { getApiUrl } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, handleApiError } from './utils/apiHelpers';

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  chefName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  items: { id: string; name: string; quantity: number; price: number; notes?: string }[];
  totalPrice: number;
  originalPrice?: number;
  discountApplied?: {
    type: 'percentage' | 'fixed';
    value: number;
    amount: number;
    reason: string;
    approvedBy: string;
  };
  preparationTime?: number;
  createdAt: string;
  updatedAt?: string;
  tableNumber?: string;
  customerName?: string;
  notes?: string;
}

interface DiscountRequest {
  _id: string;
  orderId: Order;
  waiterName: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  managerName?: string;
  managerNotes?: string;
  createdAt: string;
  processedAt?: string;
}

interface InventoryItem {
  _id: string;
  item_name: string;
  quantity: number;
  min_quantity: number;
  unit?: string;
  last_updated: string;
}

interface SalesData {
  todaySales: number;
  weekSales: number;
  monthSales: number;
  topItems: { name: string; quantity: number; revenue: number }[];
}

export default function SuperManagerDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'inventory' | 'reports'>('overview');
  const [orders, setOrders] = useState<Order[]>([]);
  const [discountRequests, setDiscountRequests] = useState<DiscountRequest[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([]);
  const [salesData, setSalesData] = useState<SalesData>({
    todaySales: 0,
    weekSales: 0,
    monthSales: 0,
    topItems: []
  });
  const [loading, setLoading] = useState(false);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedDiscountRequest, setSelectedDiscountRequest] = useState<DiscountRequest | null>(null);
  const [managerNotes, setManagerNotes] = useState('');
  const navigate = useNavigate();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // Get manager info from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const username = localStorage.getItem('username');
  const managerName = user.name || user.username || username || 'SuperManager';

  // Enhanced notifications system
  useNotifications({
    role: 'super-manager',
    userName: managerName,
    onOrderUpdate: () => {
      fetchOrders();
    },
    onDiscountUpdate: () => {
      fetchDiscountRequests();
    }
  });
  useEffect(() => {
    fetchAllData();
    
    // التحديث التلقائي كل دقيقة
    const interval = setInterval(fetchAllData, 60000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrders(),
        fetchDiscountRequests(),
        fetchInventoryData(),
        fetchSalesData()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
      showError('فشل في جلب البيانات');
    }
    setLoading(false);
  };
  const fetchOrders = async () => {
    try {
      const data = await authenticatedGet('/api/orders');
      setOrders(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching orders:', error);
      showError(handleApiError(error));
    }
  };

  const fetchDiscountRequests = async () => {
    try {
      const data = await authenticatedGet('/api/discount-requests?status=pending');
      setDiscountRequests(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching discount requests:', error);
      showError(handleApiError(error));
    }
  };

  const fetchInventoryData = async () => {
    try {
      const [inventoryData, lowStockData] = await Promise.all([
        authenticatedGet('/api/inventory').catch(() => []),
        authenticatedGet('/api/inventory/low-stock').catch(() => [])
      ]);

      setInventoryItems(Array.isArray(inventoryData) ? inventoryData : []);
      setLowStockItems(Array.isArray(lowStockData) ? lowStockData : []);
    } catch (error) {
      console.error('Error fetching inventory:', error);
      showError(handleApiError(error));
    }
  };

  const fetchSalesData = async () => {
    try {
      const allOrders = await authenticatedGet('/api/orders');
      const ordersArray = Array.isArray(allOrders) ? allOrders : [];
      const deliveredOrders = ordersArray.filter((order: Order) => order.status === 'delivered');

      const today = new Date().toISOString().split('T')[0];
      const thisWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const thisMonth = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const todaySales = deliveredOrders
        .filter((order: Order) => order.createdAt.startsWith(today))
        .reduce((sum: number, order: Order) => sum + order.totalPrice, 0);

      const weekSales = deliveredOrders
        .filter((order: Order) => new Date(order.createdAt) >= thisWeek)
        .reduce((sum: number, order: Order) => sum + order.totalPrice, 0);

      const monthSales = deliveredOrders
        .filter((order: Order) => new Date(order.createdAt) >= thisMonth)
        .reduce((sum: number, order: Order) => sum + order.totalPrice, 0);

      // حساب أفضل المنتجات
      const itemStats: { [key: string]: { quantity: number; revenue: number } } = {};
      deliveredOrders.forEach((order: Order) => {
        order.items.forEach(item => {
          if (!itemStats[item.name]) {
            itemStats[item.name] = { quantity: 0, revenue: 0 };
          }
          itemStats[item.name].quantity += item.quantity;
          itemStats[item.name].revenue += item.price * item.quantity;
        });
      });

      const topItems = Object.entries(itemStats)
        .map(([name, stats]) => ({ name, ...stats }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5);

      setSalesData({ todaySales, weekSales, monthSales, topItems });
    } catch (error) {      console.error('Error fetching sales data:', error);
    }
  };

  const handleDiscountRequest = async (requestId: string, status: 'approved' | 'rejected') => {
    setLoading(true);
    try {
      await authenticatedPut(`/api/discount-requests/${requestId}`, {
        status,
        managerName: localStorage.getItem('username') || 'مدير',
        managerNotes
      });

      setDiscountRequests(requests =>
        requests.filter(req => req._id !== requestId)
      );

      if (status === 'approved') {
        fetchOrders();
        fetchSalesData();
      }

      showSuccess(status === 'approved' ? 'تمت الموافقة على طلب الخصم' : 'تم رفض طلب الخصم');
      setShowDiscountModal(false);
      setSelectedDiscountRequest(null);    setManagerNotes('');
    } catch (error) {
      console.error('Error handling discount request:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };  const handleOrderStatusChange = async (orderId: string, newStatus: Order['status']) => {
    setLoading(true);
    try {
      await authenticatedPut(`/api/orders/${orderId}`, { status: newStatus });

      if (newStatus === 'delivered') {
        // خصم المخزون تلقائياً
        const order = orders.find(o => o._id === orderId);
        if (order) {
          await authenticatedPost('/api/inventory/deduct', {
            orderId: order._id,
            items: order.items
          });
        }
        fetchInventoryData();
        fetchSalesData();
      }

      fetchOrders();    showSuccess('تم تحديث حالة الطلب بنجاح');
    } catch (error) {
      console.error('Error updating order status:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'preparing': return 'bg-blue-100 text-blue-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-purple-100 text-purple-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    navigate('/');
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">مبيعات اليوم</h3>
              <p className="text-3xl font-bold">{salesData.todaySales.toFixed(2)} ريال</p>
            </div>
            <div className="text-4xl">💰</div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">مبيعات الأسبوع</h3>
              <p className="text-3xl font-bold">{salesData.weekSales.toFixed(2)} ريال</p>
            </div>
            <div className="text-4xl">📈</div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">الطلبات النشطة</h3>
              <p className="text-3xl font-bold">
                {orders.filter(o => ['pending', 'preparing', 'ready'].includes(o.status)).length}
              </p>
            </div>
            <div className="text-4xl">📋</div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">تنبيهات المخزون</h3>
              <p className="text-3xl font-bold">{lowStockItems.length}</p>
            </div>
            <div className="text-4xl">⚠️</div>
          </div>
        </div>
      </div>

      {/* Alerts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Discount Requests */}
        {discountRequests.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-4">
              طلبات الخصم ({discountRequests.length})
            </h3>
            <div className="space-y-3">
              {discountRequests.slice(0, 3).map((request) => (
                <div key={request._id} className="bg-white p-3 rounded border">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">طلب #{request.orderId.orderNumber}</p>
                      <p className="text-sm text-gray-600">
                        {request.waiterName} - خصم {request.discountValue}
                        {request.discountType === 'percentage' ? '%' : ' ريال'}
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        setSelectedDiscountRequest(request);
                        setShowDiscountModal(true);
                      }}
                      className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                    >
                      مراجعة
                    </button>
                  </div>
                </div>
              ))}
              {discountRequests.length > 3 && (
                <p className="text-sm text-gray-600 text-center">
                  و {discountRequests.length - 3} طلبات أخرى...
                </p>
              )}
            </div>
          </div>
        )}

        {/* Low Stock Alerts */}
        {lowStockItems.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-800 mb-4">
              تنبيهات المخزون ({lowStockItems.length})
            </h3>
            <div className="space-y-3">
              {lowStockItems.slice(0, 3).map((item) => (
                <div key={item._id} className="bg-white p-3 rounded border">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{item.item_name}</p>
                      <p className="text-sm text-gray-600">
                        متبقي: {item.quantity} {item.unit} (الحد الأدنى: {item.min_quantity})
                      </p>
                    </div>
                    <div className="text-red-600 text-sm">
                      <div className="w-16 bg-red-200 rounded-full h-2">
                        <div
                          className="bg-red-600 h-2 rounded-full"
                          style={{ width: `${Math.max((item.quantity / item.min_quantity) * 100, 5)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Top Selling Items */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">أفضل المنتجات مبيعاً</h3>
        <div className="space-y-3">
          {salesData.topItems.map((item, index) => (
            <div key={item.name} className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-bold">
                  {index + 1}
                </span>
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-sm text-gray-600">{item.quantity} مرة</p>
                </div>
              </div>
              <div className="text-left">
                <p className="font-bold text-green-600">{item.revenue.toFixed(2)} ريال</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderOrdersTab = () => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">إدارة الطلبات</h3>
        <div className="flex space-x-2 space-x-reverse">
          {['pending', 'preparing', 'ready', 'delivered'].map(status => (
            <span
              key={status}
              className={`px-3 py-1 rounded-full text-sm ${getStatusColor(status)}`}
            >
              {orders.filter(o => o.status === status).length}
            </span>
          ))}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النادل</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الطاولة</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المجموع</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوقت</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجراءات</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {orders.slice(0, 20).map((order) => (
              <tr key={order._id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {order.orderNumber}
                  {order.discountApplied && (
                    <span className="mr-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      خصم
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {order.waiterName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {order.tableNumber || 'غير محدد'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>
                    {order.discountApplied && order.originalPrice && (
                      <span className="text-gray-400 line-through">{order.originalPrice.toFixed(2)}</span>
                    )}
                    <span className="font-bold mr-1">{order.totalPrice.toFixed(2)} ريال</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={order.status}
                    onChange={(e) => handleOrderStatusChange(order._id, e.target.value as Order['status'])}
                    disabled={loading}
                    className={`px-2 py-1 rounded text-sm ${getStatusColor(order.status)} border-0`}
                  >
                    <option value="pending">في الانتظار</option>
                    <option value="preparing">قيد التحضير</option>
                    <option value="ready">جاهز</option>
                    <option value="delivered">تم التسليم</option>
                    <option value="cancelled">ملغي</option>
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(order.createdAt).toLocaleTimeString('ar-SA')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={() => {/* View order details */}}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      عرض
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderInventoryTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold">إدارة المخزون</h3>
          <div className="flex space-x-2 space-x-reverse">
            <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">
              {lowStockItems.length} مادة منخفضة
            </span>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
              {inventoryItems.length} إجمالي المواد
            </span>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأدنى</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر تحديث</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inventoryItems.map((item) => {
                const isLowStock = item.quantity <= item.min_quantity;
                return (
                  <tr key={item._id} className={isLowStock ? "bg-red-50" : "hover:bg-gray-50"}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.item_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className={isLowStock ? "text-red-600 font-bold" : ""}>
                        {item.quantity}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.min_quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.unit || 'قطعة'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {isLowStock ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          منخفض
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          طبيعي
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(item.last_updated).toLocaleDateString('ar-SA')}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderReportsTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">تقرير المبيعات</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>اليوم:</span>
              <span className="font-bold">{salesData.todaySales.toFixed(2)} ريال</span>
            </div>
            <div className="flex justify-between">
              <span>هذا الأسبوع:</span>
              <span className="font-bold">{salesData.weekSales.toFixed(2)} ريال</span>
            </div>
            <div className="flex justify-between">
              <span>هذا الشهر:</span>
              <span className="font-bold">{salesData.monthSales.toFixed(2)} ريال</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">إحصائيات الطلبات</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>إجمالي الطلبات:</span>
              <span className="font-bold">{orders.length}</span>
            </div>
            <div className="flex justify-between">
              <span>مكتملة:</span>
              <span className="font-bold">{orders.filter(o => o.status === 'delivered').length}</span>
            </div>
            <div className="flex justify-between">
              <span>نشطة:</span>
              <span className="font-bold">
                {orders.filter(o => ['pending', 'preparing', 'ready'].includes(o.status)).length}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold mb-4">حالة المخزون</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>إجمالي المواد:</span>
              <span className="font-bold">{inventoryItems.length}</span>
            </div>
            <div className="flex justify-between">
              <span>مواد منخفضة:</span>
              <span className="font-bold text-red-600">{lowStockItems.length}</span>
            </div>
            <div className="flex justify-between">
              <span>معدل الاستهلاك:</span>
              <span className="font-bold">متوسط</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">لوحة إدارة المقهى المتقدمة</h1>
              <p className="text-sm text-gray-600">إدارة شاملة لجميع عمليات المقهى</p>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                {discountRequests.length > 0 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    {discountRequests.length} طلب خصم
                  </span>
                )}
                {lowStockItems.length > 0 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {lowStockItems.length} تنبيه مخزون
                  </span>
                )}
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 space-x-reverse">
            {[
              { key: 'overview', label: 'نظرة عامة', icon: '📊' },
              { key: 'orders', label: 'الطلبات', icon: '📋' },
              { key: 'inventory', label: 'المخزون', icon: '📦' },
              { key: 'reports', label: 'التقارير', icon: '📈' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 space-x-reverse ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading && (
          <div className="fixed top-4 left-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
            جاري التحديث...
          </div>
        )}

        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'orders' && renderOrdersTab()}
        {activeTab === 'inventory' && renderInventoryTab()}
        {activeTab === 'reports' && renderReportsTab()}
      </main>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* Discount Approval Modal */}
      {showDiscountModal && selectedDiscountRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">موافقة طلب الخصم</h3>
                <button
                  onClick={() => setShowDiscountModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <p><strong>رقم الطلب:</strong> {selectedDiscountRequest.orderId.orderNumber}</p>
                  <p><strong>النادل:</strong> {selectedDiscountRequest.waiterName}</p>
                  <p><strong>المبلغ الأصلي:</strong> {selectedDiscountRequest.orderId.totalPrice.toFixed(2)} ريال</p>
                  <p><strong>الخصم المطلوب:</strong> {selectedDiscountRequest.discountValue}
                    {selectedDiscountRequest.discountType === 'percentage' ? '%' : ' ريال'}</p>
                  <p><strong>السبب:</strong> {selectedDiscountRequest.reason}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">ملاحظات المدير:</label>
                  <textarea
                    value={managerNotes}
                    onChange={(e) => setManagerNotes(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded resize-none"
                    rows={3}
                    placeholder="أضف ملاحظات (اختياري)"
                  />
                </div>

                <div className="flex space-x-3 space-x-reverse">
                  <button
                    onClick={() => handleDiscountRequest(selectedDiscountRequest._id, 'approved')}
                    disabled={loading}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:bg-gray-400"
                  >
                    الموافقة
                  </button>
                  <button
                    onClick={() => handleDiscountRequest(selectedDiscountRequest._id, 'rejected')}
                    disabled={loading}
                    className="flex-1 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:bg-gray-400"
                  >
                    الرفض
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
