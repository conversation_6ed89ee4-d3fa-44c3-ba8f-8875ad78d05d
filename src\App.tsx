import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Loading from './components/Loading';
import './App.css';
import ConnectionStatus from './components/ConnectionStatus';

// Lazy loading للصفحات
const LoginPage = React.lazy(() => import('./LoginPage'));
const ManagerDashboard = React.lazy(() => import('./ManagerDashboard'));
const ProtectedRoute = React.lazy(() => import('./ProtectedRoute'));
const WaiterDashboard = React.lazy(() => import('./WaiterDashboard'));
const ChefDashboard = React.lazy(() => import('./ChefDashboard'));
const ManagerOrders = React.lazy(() => import('./ManagerOrders'));
const Employees = React.lazy(() => import('./Employees'));
const Reports = React.lazy(() => import('./components/EnhancedReports'));
const Inventory = React.lazy(() => import('./Inventory'));
const MenuManagement = React.lazy(() => import('./MenuManagement'));
const CategoryManagement = React.lazy(() => import('./CategoryManagement'));
const ManagerLayout = React.lazy(() => import('./ManagerLayout'));
const ShiftsManagement = React.lazy(() => import('./ShiftsManagement'));
const TablesManagement = React.lazy(() => import('./components/TablesManagement'));
import './LoginPage.css';

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Suspense fallback={<Loading overlay text="جاري تحميل الصفحة..." />}>
          <ConnectionStatus />
          <Routes>
            {/* صفحة الدخول */}
            <Route path="/" element={<LoginPage />} />

            {/* صفحات النادل */}
            <Route
              path="/waiter"
              element={
                <ProtectedRoute allowedRoles={["waiter"]}>
                  <WaiterDashboard />
                </ProtectedRoute>
              }
            />

            {/* صفحات الطباخ */}
            <Route
              path="/chef"
              element={
                <ProtectedRoute allowedRoles={["chef"]}>
                  <ChefDashboard />
                </ProtectedRoute>
              }
            />

            {/* صفحات المدير */}
            <Route
              path="/manager"
              element={
                <ProtectedRoute allowedRoles={["manager"]}>
                  <ManagerLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<ManagerDashboard />} />
              <Route path="orders" element={<ManagerOrders />} />
              <Route path="employees" element={<Employees />} />
              <Route path="tables" element={<TablesManagement />} />
              <Route path="reports" element={<Reports />} />
              <Route path="inventory" element={<Inventory />} />
              <Route path="menu" element={<MenuManagement />} />
              <Route path="categories" element={<CategoryManagement />} />
            </Route>

            {/* صفحة إدارة الورديات */}
            <Route path="/shifts" element={<ShiftsManagement />} />

            {/* صفحة غير موجودة */}
            <Route path="*" element={<div style={{textAlign:'center',padding:'4rem'}}>الصفحة غير موجودة</div>} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
