import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Loading from './components/Loading';
import './App.css';
import ConnectionStatus from './components/ConnectionStatus';

// Lazy loading للصفحات
const LoginPage = React.lazy(() => import('./LoginPage'));
const ProtectedRoute = React.lazy(() => import('./ProtectedRoute'));
const UnifiedDashboard = React.lazy(() => import('./layouts/UnifiedDashboard'));
import './LoginPage.css';

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Suspense fallback={<Loading overlay text="جاري تحميل الصفحة..." />}>
          <ConnectionStatus />
          <Routes>
            {/* صفحة الدخول */}
            <Route path="/" element={<LoginPage />} />
            <Route path="/login" element={<LoginPage />} />

            {/* لوحة التحكم الموحدة للنادل */}
            <Route
              path="/waiter"
              element={
                <ProtectedRoute allowedRoles={["waiter"]}>
                  <UnifiedDashboard />
                </ProtectedRoute>
              }
            />

            {/* لوحة التحكم الموحدة للطباخ */}
            <Route
              path="/chef"
              element={
                <ProtectedRoute allowedRoles={["chef"]}>
                  <UnifiedDashboard />
                </ProtectedRoute>
              }
            />

            {/* لوحة التحكم الموحدة للمدير */}
            <Route
              path="/manager"
              element={
                <ProtectedRoute allowedRoles={["manager"]}>
                  <UnifiedDashboard />
                </ProtectedRoute>
              }
            />

            {/* إعادة توجيه أي مسار غير معروف إلى الصفحة الرئيسية */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
