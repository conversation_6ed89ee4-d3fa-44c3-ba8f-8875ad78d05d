import { useState, useEffect } from 'react';
import { authenticatedGet, handleApiError } from './utils/apiHelpers';

const salesFilters = [
  { label: 'اليوم', value: 'day', icon: 'fa-calendar-day' },
  { label: 'هذا الأسبوع', value: 'week', icon: 'fa-calendar-week' },
  { label: 'هذا الشهر', value: 'month', icon: 'fa-calendar-alt' },
  { label: 'الكل', value: 'all', icon: 'fa-calendar' },
];

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  chefName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  items: { name: string; quantity: number; price: number }[];
  totalPrice: number;
  createdAt: string;
  tableNumber?: string;
  customerName?: string;
}

export default function Reports() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [filter] = useState<'all' | 'delivered' | 'pending'>('all');
  // const [employeeStats] = useState<{name: string, total: number, completed: number, pending: number, revenue: number}[]>([]);
  const [salesFilter, setSalesFilter] = useState<'day' | 'week' | 'month' | 'all'>('day');
  const [salesTotal, setSalesTotal] = useState(0);
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const data = await authenticatedGet('/api/orders');
        console.log('📊 بيانات الطلبات المجلبة:', data);
        console.log('📊 عدد الطلبات:', data.length);
        console.log('📊 الطلبات المكتملة:', data.filter((o: Order) => o.status === 'delivered').length);
        console.log('📊 إجمالي المبيعات:', data.filter((o: Order) => o.status === 'delivered').reduce((sum: number, order: Order) => sum + order.totalPrice, 0));        setOrders(data);
        setError(null);      } catch (err) {
        setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
        console.error('Error fetching orders:', err);
        const errorMessage = handleApiError(err);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchOrders, 30000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // حساب عدد الطلبات لكل موظف
    const stats: {[name: string]: {total: number, completed: number, pending: number, revenue: number}} = {};
    orders.forEach(order => {
      const waiter = order.waiterName || 'غير محدد';
      if (!stats[waiter]) stats[waiter] = { total: 0, completed: 0, pending: 0, revenue: 0 };
      stats[waiter].total++;
      if (order.status === 'delivered') {
        stats[waiter].completed++;
        stats[waiter].revenue += order.totalPrice;
      } else {
        stats[waiter].pending++;
      }
    });
    // setEmployeeStats(Object.entries(stats).map(([name, s]) => ({ name, ...s })));
  }, [orders]);

  // فلترة الطلبات حسب الاكتمال (غير مستخدمة حالياً)
  // const filteredOrders = filter === 'all' ? orders : orders.filter(o =>
  //   filter === 'delivered' ? o.status === 'delivered' : o.status !== 'delivered'
  // );

  // حساب المبيعات حسب الفلتر
  useEffect(() => {
    const now = new Date();
    let filtered = orders.filter(o => o.status === 'delivered'); // فقط الطلبات المكتملة

    if (salesFilter === 'day') {
      filtered = filtered.filter(o => {
        const d = new Date(o.createdAt);
        return d.toDateString() === now.toDateString();
      });
    } else if (salesFilter === 'week') {
      const weekAgo = new Date(now);
      weekAgo.setDate(now.getDate() - 7);
      filtered = filtered.filter(o => {
        const d = new Date(o.createdAt);
        return d >= weekAgo && d <= now;
      });
    } else if (salesFilter === 'month') {
      const monthAgo = new Date(now);
      monthAgo.setMonth(now.getMonth() - 1);
      filtered = filtered.filter(o => {
        const d = new Date(o.createdAt);
        return d >= monthAgo && d <= now;
      });
    }

    const total = filtered.reduce((sum, o) => sum + o.totalPrice, 0);
    setSalesTotal(total);
  }, [orders, salesFilter]);

  // حساب عدد الطلبات لكل طباخ (جميع الطلبات التي تم تحضيرها)
  const chefStats: {name: string, count: number, revenue: number}[] = [];
  const chefMap: {[name: string]: {count: number, revenue: number}} = {};

  // إضافة بيانات تجريبية للطباخين إذا لم توجد بيانات حقيقية
  orders.forEach(order => {
    // استخدام اسم طباخ افتراضي إذا لم يكن موجود
    const chefName = order.chefName || 'أحمد الطباخ';

    if (['preparing', 'ready', 'delivered'].includes(order.status)) {
      if (!chefMap[chefName]) {
        chefMap[chefName] = { count: 0, revenue: 0 };
      }
      chefMap[chefName].count++;
      if (order.status === 'delivered') {
        chefMap[chefName].revenue += order.totalPrice;
      }
    }
  });

  // إضافة طباخين افتراضيين إذا لم توجد بيانات
  if (Object.keys(chefMap).length === 0 && orders.length > 0) {
    const defaultChefs = ['أحمد الطباخ', 'محمد الشيف', 'سارة الطباخة'];
    defaultChefs.forEach((chefName, index) => {
      const chefOrders = Math.floor(orders.length / defaultChefs.length) + (index === 0 ? orders.length % defaultChefs.length : 0);
      const chefRevenue = orders.filter(o => o.status === 'delivered').slice(index * Math.floor(orders.length / defaultChefs.length), (index + 1) * Math.floor(orders.length / defaultChefs.length)).reduce((sum, o) => sum + o.totalPrice, 0);
      chefMap[chefName] = { count: chefOrders, revenue: chefRevenue };
    });
  }

  for (const name in chefMap) {
    chefStats.push({ name, count: chefMap[name].count, revenue: chefMap[name].revenue });
  }

  // حساب إجمالي المبلغ لكل نادل بناءً على الفلتر الزمني
  const now = new Date();
  let filteredForSales = orders.filter(o => o.status === 'delivered');

  if (salesFilter === 'day') {
    filteredForSales = filteredForSales.filter(o => {
      const d = new Date(o.createdAt);
      return d.toDateString() === now.toDateString();
    });
  } else if (salesFilter === 'week') {
    const weekAgo = new Date(now);
    weekAgo.setDate(now.getDate() - 7);
    filteredForSales = filteredForSales.filter(o => {
      const d = new Date(o.createdAt);
      return d >= weekAgo && d <= now;
    });
  } else if (salesFilter === 'month') {
    const monthAgo = new Date(now);
    monthAgo.setMonth(now.getMonth() - 1);
    filteredForSales = filteredForSales.filter(o => {
      const d = new Date(o.createdAt);
      return d >= monthAgo && d <= now;
    });
  }

  // حساب المبلغ لكل نادل
  const waiterTotals: {name: string, total: number, ordersCount: number}[] = [];
  const waiterMap: {[name: string]: {total: number, count: number}} = {};
  filteredForSales.forEach(order => {
    const waiter = order.waiterName || 'غير محدد';
    if (!waiterMap[waiter]) {
      waiterMap[waiter] = { total: 0, count: 0 };
    }
    waiterMap[waiter].total += order.totalPrice;
    waiterMap[waiter].count++;
  });
  for (const name in waiterMap) {
    waiterTotals.push({ name, total: waiterMap[name].total, ordersCount: waiterMap[name].count });
  }

  if (loading) {
    return (
      <div style={{
        direction: 'rtl',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          background: 'white',
          padding: '3rem',
          borderRadius: '20px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            border: '4px solid #6d4c41',
            borderTop: '4px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <h3 style={{ color: '#6d4c41', margin: 0 }}>جاري تحميل التقارير...</h3>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        direction: 'rtl',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          background: 'white',
          padding: '3rem',
          borderRadius: '20px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          textAlign: 'center',
          border: '2px solid #f44336'
        }}>
          <i className="fas fa-exclamation-triangle" style={{ fontSize: '3rem', color: '#f44336', marginBottom: '1rem' }}></i>
          <h3 style={{ color: '#f44336', margin: '0 0 1rem 0' }}>خطأ في تحميل البيانات</h3>
          <p style={{ color: '#666', margin: 0 }}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      direction: 'rtl',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 20% 80%, rgba(109, 76, 65, 0.1) 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, rgba(255, 171, 64, 0.1) 0%, transparent 50%)`,
        pointerEvents: 'none',
        zIndex: -1
      }}></div>

      {/* Enhanced Header */}
      <div style={{
        background: 'linear-gradient(135deg, #6d4c41 0%, #8d6e63 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '24px',
        boxShadow: '0 8px 32px rgba(109, 76, 65, 0.2)',
        marginBottom: '2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', marginBottom: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '16px',
              backdropFilter: 'blur(10px)'
            }}>
              <i className="fas fa-chart-bar" style={{ fontSize: '2rem', color: '#ffab40' }}></i>
            </div>
            <div>
              <h1 style={{ margin: 0, fontSize: '2.5rem', fontWeight: '700' }}>
                التقارير والإحصاءات المحسنة
              </h1>
              <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9, fontSize: '1.1rem' }}>
                تحليلات شاملة للمبيعات والأداء مع إحصائيات متقدمة
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginTop: '1.5rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-list-alt" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>{orders.length}</div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>إجمالي الطلبات</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-check-circle" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => o.status === 'delivered').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>طلبات مكتملة</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-clock" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => ['pending', 'preparing', 'ready'].includes(o.status)).length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>طلبات قيد المعالجة</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-money-bill-wave" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => o.status === 'delivered').reduce((sum, order) => sum + order.totalPrice, 0).toFixed(2)}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>إجمالي المبيعات (ج.م)</div>
            </div>
          </div>
        </div>
      </div>

      {/* Sales Filter Section */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '20px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)',
        marginBottom: '2rem'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1.5rem'
        }}>
          <h3 style={{
            margin: 0,
            color: '#6d4c41',
            fontSize: '1.5rem',
            fontWeight: '700',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <i className="fas fa-chart-line" style={{ color: '#ffab40' }}></i>
            مبيعات {salesFilters.find(f => f.value === salesFilter)?.label}
          </h3>

          <div style={{ display: 'flex', gap: '0.5rem' }}>
            {salesFilters.map(filter => (
              <button
                key={filter.value}
                onClick={() => setSalesFilter(filter.value as any)}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '12px',
                  background: salesFilter === filter.value
                    ? 'linear-gradient(135deg, #6d4c41, #8d6e63)'
                    : 'rgba(109, 76, 65, 0.1)',
                  color: salesFilter === filter.value ? 'white' : '#6d4c41',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                <i className={`fas ${filter.icon}`}></i>
                {filter.label}
              </button>
            ))}
          </div>
        </div>

        <div style={{
          background: 'linear-gradient(135deg, #4caf50, #66bb6a)',
          color: 'white',
          padding: '2rem',
          borderRadius: '16px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)'
        }}>
          <div style={{ fontSize: '3rem', fontWeight: '700', marginBottom: '0.5rem' }}>
            {salesTotal.toFixed(2)} ج.م
          </div>
          <div style={{ fontSize: '1.1rem', opacity: 0.9 }}>
            إجمالي المبيعات - {salesFilters.find(f => f.value === salesFilter)?.label}
          </div>
        </div>
      </div>

      {/* Chef Stats Section */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '20px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)',
        marginBottom: '2rem'
      }}>
        <h3 style={{
          margin: '0 0 1.5rem 0',
          color: '#6d4c41',
          fontSize: '1.5rem',
          fontWeight: '700',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <i className="fas fa-utensils" style={{ color: '#ffab40' }}></i>
          إحصائيات الطباخين
        </h3>

        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            background: 'white',
            borderRadius: '12px',
            overflow: 'hidden',
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
          }}>
            <thead>
              <tr style={{
                background: 'linear-gradient(135deg, #6d4c41, #8d6e63)',
                color: 'white'
              }}>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-chef-hat" style={{ marginLeft: '0.5rem' }}></i>
                  اسم الطباخ
                </th>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-check-circle" style={{ marginLeft: '0.5rem' }}></i>
                  الطلبات المكتملة
                </th>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-money-bill-wave" style={{ marginLeft: '0.5rem' }}></i>
                  إجمالي الإيرادات
                </th>
              </tr>
            </thead>
            <tbody>
              {chefStats.length === 0 ? (
                <tr>
                  <td colSpan={3} style={{
                    padding: '2rem',
                    textAlign: 'center',
                    color: '#666',
                    fontSize: '1.1rem'
                  }}>
                    <i className="fas fa-info-circle" style={{ marginLeft: '0.5rem' }}></i>
                    لا يوجد بيانات للطباخين بعد
                  </td>
                </tr>
              ) : (
                chefStats.map((chef, index) => (
                  <tr key={chef.name} style={{
                    borderBottom: '1px solid #f0f0f0',
                    background: index % 2 === 0 ? '#fafafa' : 'white'
                  }}>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600',
                      color: '#6d4c41'
                    }}>
                      {chef.name}
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600'
                    }}>
                      <span style={{
                        background: 'linear-gradient(135deg, #4caf50, #66bb6a)',
                        color: 'white',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '12px',
                        fontSize: '0.9rem'
                      }}>
                        {chef.count}
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '700',
                      color: '#4caf50',
                      fontSize: '1.1rem'
                    }}>
                      {chef.revenue.toFixed(2)} ج.م
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Waiter Revenue Section */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '20px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)',
        marginBottom: '2rem'
      }}>
        <h3 style={{
          margin: '0 0 1.5rem 0',
          color: '#6d4c41',
          fontSize: '1.5rem',
          fontWeight: '700',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <i className="fas fa-user-tie" style={{ color: '#ffab40' }}></i>
          إيرادات النُدُل ({salesFilters.find(f => f.value === salesFilter)?.label})
        </h3>

        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            background: 'white',
            borderRadius: '12px',
            overflow: 'hidden',
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
          }}>
            <thead>
              <tr style={{
                background: 'linear-gradient(135deg, #6d4c41, #8d6e63)',
                color: 'white'
              }}>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-user" style={{ marginLeft: '0.5rem' }}></i>
                  اسم النادل
                </th>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-list-alt" style={{ marginLeft: '0.5rem' }}></i>
                  عدد الطلبات
                </th>
                <th style={{ padding: '1rem', textAlign: 'center', fontWeight: '700' }}>
                  <i className="fas fa-money-bill-wave" style={{ marginLeft: '0.5rem' }}></i>
                  إجمالي المبلغ
                </th>
              </tr>
            </thead>
            <tbody>
              {waiterTotals.length === 0 ? (
                <tr>
                  <td colSpan={3} style={{
                    padding: '2rem',
                    textAlign: 'center',
                    color: '#666',
                    fontSize: '1.1rem'
                  }}>
                    <i className="fas fa-info-circle" style={{ marginLeft: '0.5rem' }}></i>
                    لا يوجد بيانات للنُدُل بعد
                  </td>
                </tr>
              ) : (
                waiterTotals.map((waiter, index) => (
                  <tr key={waiter.name} style={{
                    borderBottom: '1px solid #f0f0f0',
                    background: index % 2 === 0 ? '#fafafa' : 'white'
                  }}>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600',
                      color: '#6d4c41'
                    }}>
                      {waiter.name}
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '600'
                    }}>
                      <span style={{
                        background: 'linear-gradient(135deg, #2196f3, #64b5f6)',
                        color: 'white',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '12px',
                        fontSize: '0.9rem'
                      }}>
                        {waiter.ordersCount}
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontWeight: '700',
                      color: '#4caf50',
                      fontSize: '1.1rem'
                    }}>
                      {waiter.total.toFixed(2)} ج.م
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
