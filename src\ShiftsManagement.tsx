import { useState } from 'react';

interface Shift {
  shift_id: number;
  user_name: string;
  role_name: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
}

const statusLabels: Record<Shift['status'], string> = {
  scheduled: 'مجدولة',
  in_progress: 'قيد التنفيذ',
  completed: 'مكتملة',
  cancelled: 'ملغاة',
};

export default function ShiftsManagement() {
  const [shifts] = useState<Shift[]>([]); // سيتم جلب البيانات من السيرفر لاحقاً

  return (
    <div style={{ direction: 'rtl', padding: '2rem' }}>
      <h2>إدارة الورديات</h2>
      <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
        <thead>
          <tr style={{ background: '#f5f5f5' }}>
            <th>الموظف</th>
            <th>الدور</th>
            <th>بداية الوردية</th>
            <th>نهاية الوردية</th>
            <th>الحالة</th>
          </tr>
        </thead>
        <tbody>
          {shifts.map(shift => (
            <tr key={shift.shift_id} style={{ textAlign: 'center', borderBottom: '1px solid #eee' }}>
              <td>{shift.user_name}</td>
              <td>{shift.role_name}</td>
              <td>{new Date(shift.start_time).toLocaleString('ar-EG')}</td>
              <td>{new Date(shift.end_time).toLocaleString('ar-EG')}</td>
              <td>{statusLabels[shift.status]}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
