import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useState, useEffect } from 'react';
import './ManagerLayout.css';
import ThemeToggle from './components/ThemeToggle';
import Card from './components/Card';

export default function ManagerLayout() {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 768);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // تحديد الصفحة النشطة
  const isActive = (path: string) => {
    return location.pathname === path;
  };
  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    navigate('/', { replace: true });
  };
  // التحقق من حجم الشاشة عند تغيير حجم النافذة
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);

      if (!mobile) {
        setSidebarOpen(true);
      }
    };

    // تنفيذ مرة واحدة عند التحميل
    handleResize();

    // إضافة مستمع الحدث
    window.addEventListener('resize', handleResize);

    // إزالة مستمع الحدث عند تفكيك المكون
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // فتح وإغلاق القائمة
  const toggleSidebar = () => {
    console.log("تبديل القائمة الجانبية", !sidebarOpen);
    setSidebarOpen(prevState => !prevState);
  };

  return (
    <div className="manager-layout">
      {/* زر التوغل يظهر فقط عند إغلاق القائمة */}
      {!sidebarOpen && (
        <button
          aria-label="فتح القائمة الجانبية"
          onClick={toggleSidebar}
          className="sidebar-toggle-btn"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      )}
      {/* القائمة الجانبية - دائماً موجودة ولكن قد تكون مخفية */}
      <aside className={`sidebar ${!sidebarOpen ? 'hidden' : ''}`}>
          <div className="sidebar-header">
            <img src="/coffee-logo.svg" alt="شعار المقهى" className="sidebar-logo" />
            <h3>لوحة المدير</h3>
            <div className="sidebar-theme-toggle">
              <ThemeToggle />
            </div>
          </div>

          <div className="sidebar-menu">
            <button
              onClick={() => navigate('/manager')}
              className={`sidebar-btn ${isActive('/manager') ? 'active' : ''}`}
            >
              <span className="icon">📊</span>
              الرئيسية
            </button>

            <button
              onClick={() => navigate('/manager/orders')}
              className={`sidebar-btn ${isActive('/manager/orders') ? 'active' : ''}`}
            >
              <span className="icon">📝</span>
              الطلبات
            </button>

            <button
              onClick={() => navigate('/manager/employees')}
              className={`sidebar-btn ${isActive('/manager/employees') ? 'active' : ''}`}
            >
              <span className="icon">👥</span>
              الموظفون
            </button>

            <button
              onClick={() => navigate('/manager/tables')}
              className={`sidebar-btn ${isActive('/manager/tables') ? 'active' : ''}`}
            >
              <span className="icon">🍽️</span>
              إدارة الطاولات
            </button>

            <button
              onClick={() => navigate('/manager/reports')}
              className={`sidebar-btn ${isActive('/manager/reports') ? 'active' : ''}`}
            >
              <span className="icon">📈</span>
              التقارير
            </button>

            <button
              onClick={() => navigate('/manager/inventory')}
              className={`sidebar-btn ${isActive('/manager/inventory') ? 'active' : ''}`}
            >
              <span className="icon">🧾</span>
              المخزون
            </button>

            <button
              onClick={() => navigate('/manager/menu')}
              className={`sidebar-btn ${isActive('/manager/menu') ? 'active' : ''}`}
            >
              <span className="icon">☕</span>
              قائمة المشروبات
            </button>

            <button
              onClick={() => navigate('/manager/categories')}
              className={`sidebar-btn ${isActive('/manager/categories') ? 'active' : ''}`}
            >
              <span className="icon">🏷️</span>
              فئات المشروبات
            </button>
          </div>

          <div className="sidebar-footer">
            <button onClick={handleLogout} className="logout-sidebar-btn">
              تسجيل الخروج
            </button>

            <button
              aria-label="إغلاق القائمة الجانبية"
              onClick={toggleSidebar}
              className="close-sidebar-btn"
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
        </aside>

      {/* محتوى الصفحة */}
      <main className={`manager-content ${!sidebarOpen ? 'expanded' : ''}`}>
        {isMobile && (
          <button
            className="menu-toggle-btn"
            onClick={toggleSidebar}
            aria-label="إظهار القائمة الجانبية"
            title="إظهار القائمة الجانبية"
          >
            <i className="fas fa-bars"></i>
          </button>
        )}
        {location.pathname === '/manager' ? (
          <div className="dashboard-welcome">
            <Card
              title="مرحباً بك في لوحة تحكم المدير"
              icon="fas fa-coffee"
            >
              <p>يمكنك من هنا إدارة المقهى بشكل كامل، متابعة الطلبات، إدارة الموظفين، وعرض التقارير.</p>
            </Card>

            <div className="dashboard-grid">
              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/orders')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-clipboard-list dashboard-icon"></i>
                  <h3>الطلبات</h3>
                  <p>إدارة ومتابعة جميع الطلبات</p>
                </div>
              </Card>

              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/employees')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-users dashboard-icon"></i>
                  <h3>الموظفون</h3>
                  <p>إدارة بيانات الموظفين</p>
                </div>
              </Card>

              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/tables')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-table dashboard-icon"></i>
                  <h3>إدارة الطاولات</h3>
                  <p>متابعة حسابات الطاولات وتصفية الحسابات</p>
                </div>
              </Card>

              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/reports')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-chart-line dashboard-icon"></i>
                  <h3>التقارير</h3>
                  <p>عرض تقارير المبيعات والأداء</p>
                </div>
              </Card>

              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/inventory')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-boxes dashboard-icon"></i>
                  <h3>المخزون</h3>
                  <p>إدارة مخزون المقهى</p>
                </div>
              </Card>

              <Card
                hoverable
                className="dashboard-card"
                onClick={() => navigate('/manager/menu')}
              >
                <div className="dashboard-card-content">
                  <i className="fas fa-coffee dashboard-icon"></i>
                  <h3>قائمة المشروبات</h3>
                  <p>إدارة المشروبات والأسعار</p>
                </div>
              </Card>
            </div>
          </div>
        ) : (
          <Outlet />
        )}
      </main>
    </div>
  );
}
