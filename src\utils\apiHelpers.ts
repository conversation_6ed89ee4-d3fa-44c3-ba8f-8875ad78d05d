// API Helper functions for authenticated requests
// دوال مساعدة لطلبات API مع المصادقة

import { getApiUrl } from '../config/app.config';

// Get auth token from localStorage
export const getAuthToken = (): string | null => {
  return localStorage.getItem('token') || localStorage.getItem('authToken');
};

// Get auth headers with token
export const getAuthHeaders = (): HeadersInit => {
  const token = getAuthToken();
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Make authenticated API request
export const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = getApiUrl(endpoint);
  const headers = {
    ...getAuthHeaders(),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

// GET request with authentication
export const authenticatedGet = async (endpoint: string): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated GET request failed:', error);
    throw error;
  }
};

// POST request with authentication
export const authenticatedPost = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated POST request failed:', error);
    throw error;
  }
};

// PUT request with authentication
export const authenticatedPut = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated PUT request failed:', error);
    throw error;
  }
};

// DELETE request with authentication
export const authenticatedDelete = async (endpoint: string): Promise<any> => {
  try {
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Authenticated DELETE request failed:', error);
    throw error;
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  const user = localStorage.getItem('user');
  return !!(token && user);
};

// Get current user from localStorage
export const getCurrentUser = (): any | null => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

// Logout user (clear all auth data)
export const logoutUser = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');
};

// Handle API errors with user-friendly messages
export const handleApiError = (error: any): string => {
  if (error.message?.includes('401')) {
    return 'انتهت صلاحية جلسة العمل. الرجاء تسجيل الدخول مرة أخرى.';
  }
  
  if (error.message?.includes('403')) {
    return 'ليس لديك صلاحية للوصول إلى هذا المورد.';
  }
  
  if (error.message?.includes('404')) {
    return 'المورد المطلوب غير موجود.';
  }
  
  if (error.message?.includes('500')) {
    return 'خطأ في الخادم. الرجاء المحاولة لاحقاً.';
  }
  
  if (error.message?.includes('Network')) {
    return 'خطأ في الاتصال بالشبكة. تحقق من اتصالك بالإنترنت.';
  }
  
  return error.message || 'حدث خطأ غير متوقع.';
};

// Retry mechanism for failed requests
export const retryRequest = async (
  requestFn: () => Promise<any>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<any> => {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }
  
  throw lastError;
};

export default {
  getAuthToken,
  getAuthHeaders,
  makeAuthenticatedRequest,
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedDelete,
  isAuthenticated,
  getCurrentUser,
  logoutUser,
  handleApiError,
  retryRequest,
};
