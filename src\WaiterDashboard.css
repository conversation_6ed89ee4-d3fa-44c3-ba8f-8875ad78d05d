/* WaiterDashboard.css - نمط شامل لوحة النادل */

/* متغيرات الألوان */
:root {
  --primary-color: #2c3e50;
  --primary-light: #34495e;
  --primary-dark: #1a252f;
  --secondary-color: #3498db;
  --secondary-light: #5dade2;
  --secondary-dark: #2980b9;
  --success-color: #27ae60;
  --success-light: #58d68d;
  --success-dark: #1e8449;
  --warning-color: #f39c12;
  --warning-light: #f7dc6f;
  --warning-dark: #d68910;
  --danger-color: #e74c3c;
  --danger-light: #f1948a;
  --danger-dark: #c0392b;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --box-shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

/* إعادة تعيين أساسي */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  background-color: var(--gray-100);
  color: var(--dark-color);
  line-height: 1.6;
}

/* التخطيط الرئيسي */
.waiter-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: var(--gray-100);
}

/* زر القائمة للموبايل */
.mobile-menu-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1001;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: var(--box-shadow-lg);
  transition: var(--transition);
  display: none;
}

.mobile-menu-toggle:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

@media (max-width: 1024px) {
  .mobile-menu-toggle {
    display: block;
  }
}

/* الشريط الجانبي */
.dashboard-sidebar {
  width: 280px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  position: relative;
  z-index: 1000;
  box-shadow: var(--box-shadow-lg);
}

.dashboard-sidebar.hidden {
  margin-right: -280px;
}

.dashboard-sidebar.visible {
  margin-right: 0;
}

@media (max-width: 1024px) {
  .dashboard-sidebar {
    position: fixed;
    top: 0;
    right: -100vw;
    height: 100vh;
    width: 300px;
    z-index: 1050;
  }
  
  .dashboard-sidebar.visible {
    right: 0;
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}

/* رأس الشريط الجانبي */
.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: bold;
}

.sidebar-logo i {
  font-size: 1.5rem;
  color: var(--warning-color);
}

.sidebar-close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  display: none;
}

.sidebar-close-btn:hover {
  background: rgba(255,255,255,0.1);
}

@media (max-width: 1024px) {
  .sidebar-close-btn {
    display: block;
  }
}

/* التنقل */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-menu {
  list-style: none;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--white);
  text-decoration: none;
  background: none;
  border: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
  transition: var(--transition);
  border-radius: 0;
  font-size: 1rem;
}

.nav-link:hover {
  background: rgba(255,255,255,0.1);
  padding-right: 2rem;
}

.nav-link.active {
  background: var(--secondary-color);
  box-shadow: inset 4px 0 0 var(--warning-color);
}

.nav-link.active:hover {
  background: var(--secondary-dark);
}

.nav-icon {
  font-size: 1.125rem;
  width: 20px;
  text-align: center;
}

.nav-text {
  flex: 1;
}

/* إحصائيات الشريط الجانبي */
.sidebar-stats {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--gray-300);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.stat-icon {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,0.1);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.stat-details {
  flex: 1;
}

.stat-value {
  display: block;
  font-weight: bold;
  font-size: 1rem;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--gray-300);
}

/* تذييل الشريط الجانبي */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--danger-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  width: 100%;
  text-align: center;
  justify-content: center;
  font-size: 1rem;
}

.logout-btn:hover {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

/* المحتوى الرئيسي */
.dashboard-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  margin-right: 0;
  transition: var(--transition);
}

@media (max-width: 1024px) {
  .dashboard-main {
    margin-right: 0;
    padding: 5rem 1rem 1rem 1rem;
  }
}

/* بطاقة المحتوى */
.content-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  min-height: calc(100vh - 4rem);
  overflow: hidden;
}

@media (max-width: 1024px) {
  .content-card {
    min-height: calc(100vh - 7rem);
  }
}

/* حاوي المحتوى */
.content-container {
  padding: 2rem;
}

/* رأس الشاشة */
.screen-header {
  margin-bottom: 2rem;
  text-align: center;
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: 1.5rem;
}

.screen-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.screen-title i {
  color: var(--secondary-color);
}

.screen-subtitle {
  color: var(--gray-600);
  font-size: 1.125rem;
}

/* قسم البحث */
.search-section {
  margin-bottom: 2rem;
}

.search-input-group {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.search-input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  font-size: 1rem;
  pointer-events: none;
}

.clear-search {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: var(--transition);
}

.clear-search:hover {
  color: var(--danger-color);
  background: var(--gray-100);
}

/* فلتر الفئات */
.category-filter {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--white);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.category-btn:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
  transform: translateY(-1px);
}

.category-btn.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white);
}

.category-btn i {
  font-size: 1rem;
}

/* شبكة القائمة */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .menu-grid {
    grid-template-columns: 1fr;
  }
}

/* بطاقة عنصر القائمة */
.menu-item-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.menu-item-name {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color);
  flex: 1;
  margin-left: 0.5rem;
}

.menu-item-price {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--success-color);
  background: var(--success-light);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  white-space: nowrap;
}

.menu-item-description {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.menu-item-categories {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.category-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--white);
}

.add-to-cart-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-to-cart-btn:hover {
  background: var(--secondary-dark);
  transform: translateY(-1px);
}

/* قسم الفلتر */
.filter-section {
  margin-bottom: 2rem;
}

.status-filters {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

.status-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--white);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.status-filter:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
  transform: translateY(-1px);
}

.status-filter.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white);
}

.status-filter .count {
  background: rgba(255,255,255,0.2);
  padding: 0.125rem 0.375rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  margin-right: 0.25rem;
}

.status-filter.active .count {
  background: rgba(255,255,255,0.3);
}

/* شبكة الطلبات */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .orders-grid {
    grid-template-columns: 1fr;
  }
}

/* بطاقة الطلب */
.order-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.order-number {
  font-weight: bold;
  font-size: 1.125rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.order-status.pending {
  background: var(--warning-light);
  color: var(--warning-dark);
}

.order-status.preparing {
  background: var(--info-color);
  color: var(--white);
}

.order-status.ready {
  background: var(--success-light);
  color: var(--success-dark);
}

.order-status.delivered {
  background: var(--gray-300);
  color: var(--gray-700);
}

/* معلومات الطلب */
.order-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-100);
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-600);
  font-size: 0.875rem;
  font-weight: 500;
}

.label i {
  width: 16px;
  text-align: center;
}

.value {
  font-weight: 600;
  color: var(--dark-color);
}

/* إجراءات الطلب */
.order-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-details {
  flex: 1;
  padding: 0.5rem 1rem;
  background: var(--info-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  min-width: 100px;
}

.btn-details:hover {
  background: #138496;
  transform: translateY(-1px);
}

.btn-deliver {
  flex: 1;
  padding: 0.5rem 1rem;
  background: var(--success-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  min-width: 100px;
}

.btn-deliver:hover {
  background: var(--success-dark);
  transform: translateY(-1px);
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* بطاقة الإحصائية */
.stat-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.stat-card .stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  color: var(--white);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-card .stat-content {
  flex: 1;
}

.stat-card .stat-number {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1.2;
}

.stat-card .stat-label {
  color: var(--gray-600);
  font-size: 0.875rem;
  font-weight: 500;
}

/* شبكة الطاولات */
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .tables-grid {
    grid-template-columns: 1fr;
  }
}

/* بطاقة الطاولة */
.table-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.table-number {
  font-weight: bold;
  font-size: 1.125rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-status {
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.table-status.active {
  background: var(--success-light);
  color: var(--success-dark);
}

.table-status.closed {
  background: var(--gray-300);
  color: var(--gray-700);
}

.table-info {
  margin-bottom: 1rem;
}

.table-actions {
  display: flex;
  gap: 0.75rem;
}

/* محتوى السلة */
.cart-content {
  max-width: 600px;
  margin: 0 auto;
}

.cart-items {
  margin-bottom: 2rem;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.cart-item-info h4 {
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.cart-item-price {
  color: var(--success-color);
  font-weight: bold;
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--gray-300);
  background: var(--white);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover {
  background: var(--secondary-color);
  color: var(--white);
  border-color: var(--secondary-color);
}

.quantity {
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

/* نموذج السلة */
.cart-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-color);
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* مجموع السلة */
.cart-total {
  text-align: center;
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

/* إجراءات السلة */
.cart-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.clear-cart-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--danger-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 140px;
}

.clear-cart-btn:hover {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

.submit-order-btn {
  flex: 2;
  padding: 0.75rem 1rem;
  background: var(--success-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 140px;
}

.submit-order-btn:hover:not(:disabled) {
  background: var(--success-dark);
  transform: translateY(-1px);
}

.submit-order-btn:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
}

/* حالة فارغة */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--gray-600);
}

.empty-state i {
  font-size: 4rem;
  color: var(--gray-400);
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.empty-state p {
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.empty-cart-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-cart-icon {
  font-size: 5rem;
  color: var(--gray-400);
  margin-bottom: 1.5rem;
}

.browse-menu-btn {
  padding: 0.75rem 1.5rem;
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.browse-menu-btn:hover {
  background: var(--secondary-dark);
  transform: translateY(-1px);
}

/* النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.modal-content {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0,0,0,0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* أقسام النافذة المنبثقة */
.order-info-section,
.table-info-section,
.order-items-section,
.table-orders-section,
.discount-section {
  margin-bottom: 2rem;
}

.order-info-section h3,
.table-info-section h3,
.order-items-section h3,
.table-orders-section h3,
.discount-section h3 {
  font-size: 1.25rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-info-grid,
.table-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item .label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.info-item .value {
  font-weight: 600;
  color: var(--dark-color);
}

.total-amount {
  color: var(--success-color) !important;
  font-size: 1.125rem !important;
}

/* شارة الحالة */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: var(--warning-light);
  color: var(--warning-dark);
}

.status-badge.preparing {
  background: var(--info-color);
  color: var(--white);
}

.status-badge.ready {
  background: var(--success-light);
  color: var(--success-dark);
}

.status-badge.delivered {
  background: var(--gray-300);
  color: var(--gray-700);
}

.status-badge.active {
  background: var(--success-light);
  color: var(--success-dark);
}

.status-badge.closed {
  background: var(--gray-300);
  color: var(--gray-700);
}

/* قائمة العناصر */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.item-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.item-notes {
  font-size: 0.875rem;
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  color: var(--gray-700);
}

.item-price {
  color: var(--info-color);
  font-weight: 500;
}

.item-total {
  color: var(--success-color);
  font-weight: bold;
  min-width: 80px;
  text-align: left;
}

/* بطاقات ملخص الطلبات */
.order-summary-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
}

.order-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--gray-300);
}

.order-summary-info {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.summary-item {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.btn-order-details {
  width: 100%;
  padding: 0.5rem 1rem;
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
}

.btn-order-details:hover {
  background: var(--secondary-dark);
}

/* نموذج الخصم */
.order-summary {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.order-summary h3 {
  font-size: 1.125rem;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
}

.summary-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-info div {
  font-size: 0.875rem;
  color: var(--gray-700);
}

.discount-form {
  margin-bottom: 1.5rem;
}

.discount-form .form-group {
  margin-bottom: 1rem;
}

.discount-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-color);
}

.discount-form input,
.discount-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  font-family: inherit;
}

.discount-form input:focus,
.discount-form textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.discount-form textarea {
  resize: vertical;
  min-height: 80px;
}

/* معاينة الخصم */
.discount-preview {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-300);
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-item.total {
  font-weight: bold;
  font-size: 1.125rem;
  color: var(--success-color);
  border-top: 2px solid var(--gray-300);
  padding-top: 0.75rem;
  margin-top: 0.5rem;
}

/* حالة الخصم */
.discount-status {
  padding: 1rem;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.discount-status.pending {
  background: var(--warning-light);
  color: var(--warning-dark);
  border: 1px solid var(--warning-color);
}

.discount-status.approved {
  background: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success-color);
}

.discount-status.rejected {
  background: var(--danger-light);
  color: var(--danger-dark);
  border: 1px solid var(--danger-color);
}

/* أزرار النافذة المنبثقة */
.btn-discount {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--warning-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-discount:hover {
  background: var(--warning-dark);
  transform: translateY(-1px);
}

.btn-submit {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--success-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-submit:hover:not(:disabled) {
  background: var(--success-dark);
  transform: translateY(-1px);
}

.btn-submit:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
}

.btn-cancel,
.btn-close {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--gray-500);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  font-weight: 500;
}

.btn-cancel:hover,
.btn-close:hover {
  background: var(--gray-600);
  transform: translateY(-1px);
}

/* نظام التوست */
.toast-container {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.toast {
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  color: var(--white);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--box-shadow-lg);
  animation: toastSlideIn 0.3s ease;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  min-height: 60px;
}

.toast:hover {
  transform: translateX(-5px);
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast-success {
  background: var(--success-color);
}

.toast-error {
  background: var(--danger-color);
}

.toast-info {
  background: var(--info-color);
}

.toast i {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast span {
  flex: 1;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--white);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: var(--transition);
  opacity: 0.8;
  flex-shrink: 0;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(255,255,255,0.2);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .content-container {
    padding: 1rem;
  }
  
  .screen-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .screen-subtitle {
    font-size: 1rem;
  }
  
  .category-filter {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .category-btn {
    flex-shrink: 0;
  }
  
  .status-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .status-filter {
    flex-shrink: 0;
  }
  
  .modal-content {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }
  
  .order-info-grid,
  .table-info-grid {
    grid-template-columns: 1fr;
  }
  
  .item-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .item-details {
    width: 100%;
    justify-content: space-between;
  }
  
  .cart-actions {
    flex-direction: column;
  }
  
  .order-actions {
    flex-direction: column;
  }
  
  .table-actions {
    flex-direction: column;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .toast-container {
    left: 0.5rem;
    right: 0.5rem;
    max-width: none;
  }
  
  .toast {
    margin: 0;
  }
}

@media (max-width: 480px) {
  .dashboard-main {
    padding: 4rem 0.5rem 0.5rem 0.5rem;
  }
  
  .content-container {
    padding: 0.75rem;
  }
  
  .screen-title {
    font-size: 1.25rem;
  }
  
  .menu-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .menu-item-price {
    align-self: flex-end;
  }
  
  .order-summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .order-summary-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* تحسينات إضافية للطباعة */
@media print {
  .dashboard-sidebar,
  .mobile-menu-toggle,
  .toast-container {
    display: none !important;
  }
  
  .dashboard-main {
    margin-right: 0 !important;
    padding: 0 !important;
  }
  
  .content-card {
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
  }
  
  .modal-overlay {
    position: relative !important;
    background: none !important;
    padding: 0 !important;
  }
  
  .modal-content {
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
    max-height: none !important;
    overflow: visible !important;
  }
}

/* تحسينات إمكانية الوصول */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* تحسينات للتركيز */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #3498db;
    --primary-light: #5dade2;
    --primary-dark: #2980b9;
    --secondary-color: #f39c12;
    --secondary-light: #f7dc6f;
    --secondary-dark: #d68910;
    --light-color: #2c3e50;
    --dark-color: #ecf0f1;
    --white: #34495e;
    --gray-100: #2c3e50;
    --gray-200: #34495e;
    --gray-300: #3e5469;
    --gray-400: #4f6b7d;
    --gray-500: #5d7283;
    --gray-600: #85929e;
    --gray-700: #a6b1bb;
    --gray-800: #d5dbdb;
    --gray-900: #ecf0f1;
  }
}
