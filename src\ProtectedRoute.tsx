import { Navigate } from 'react-router-dom';
import React from 'react';

interface ProtectedRouteProps {
  allowedRoles: string[];
  children: React.ReactNode;
}

export default function ProtectedRoute({ allowedRoles, children }: ProtectedRouteProps) {
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  console.log('ProtectedRoute - User from localStorage:', user);
  console.log('ProtectedRoute - Required roles:', allowedRoles);

  if (!user) {
    console.log('ProtectedRoute - No user found, redirecting to login');
    return <Navigate to="/" replace />;
  }

  // التحقق من الدور - الآن الأدوار محفوظة بالإنجليزية
  const userRole = user.role;
  const hasAccess = allowedRoles.includes(userRole);

  if (!hasAccess) {
    console.log('Access denied - User Role:', userRole);
    console.log('Access denied - Allowed Roles:', allowedRoles);
    console.log('Access denied - User Object:', user);
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}
