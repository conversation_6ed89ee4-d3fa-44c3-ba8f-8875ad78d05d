import { Navigate } from 'react-router-dom';
import React from 'react';

interface ProtectedRouteProps {
  allowedRoles: string[];
  children: React.ReactNode;
}

export default function ProtectedRoute({ allowedRoles, children }: ProtectedRouteProps) {
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  // تحويل الأدوار المسموح بها إلى العربية
  const arabicRoles = allowedRoles.map(role => {
    const roleMapping = {
      'waiter': 'نادل',
      'chef': 'طباخ',
      'manager': 'مدير',
      'admin': 'مدير',
      'employee': 'نادل'
    };
    return roleMapping[role as keyof typeof roleMapping] || role;
  });

  if (!user || !arabicRoles.includes(user.role)) {
    return (
      <Navigate to="/" replace state={{ error: 'مصدر غير موجود أو ليس لديك صلاحية الوصول لهذه الصفحة.' }} />
    );
  }
  return <>{children}</>;
}
