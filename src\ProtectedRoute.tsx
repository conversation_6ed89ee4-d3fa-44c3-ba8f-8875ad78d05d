import { Navigate } from 'react-router-dom';
import React from 'react';

interface ProtectedRouteProps {
  allowedRoles: string[];
  children: React.ReactNode;
}

export default function ProtectedRoute({ allowedRoles, children }: ProtectedRouteProps) {
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  if (!user) {
    return <Navigate to="/" replace />;
  }

  // التحقق من الدور - الآن الأدوار محفوظة بالإنجليزية
  const userRole = user.role;
  const hasAccess = allowedRoles.includes(userRole);

  if (!hasAccess) {
    console.log('Access denied:', { userRole, allowedRoles });
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}
