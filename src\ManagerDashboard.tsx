import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useNotifications } from './hooks/useNotifications';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';

export default function ManagerDashboard() {
  const navigate = useNavigate();
  const { toasts, removeToast } = useToast();

  // Get manager info from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const username = localStorage.getItem('username');
  const managerName = user.name || user.username || username || 'Manager';

  // Enhanced notifications system
  useNotifications({
    role: 'manager',
    userName: managerName,
    onOrderUpdate: () => {
      console.log('Manager: Order update received');
    },
    onDiscountUpdate: () => {
      console.log('Manager: Discount request update received');
    }
  });

  return (
    <div style={{ direction: 'rtl' }}>
      <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#6d4c41' }}>مرحباً بك في لوحة المدير</h2>
      <p style={{ textAlign: 'center', color: '#444', fontSize: '1.1rem', marginBottom: '3rem' }}>
        اختر القسم الذي ترغب بإدارته من البطاقات أدناه أو من القائمة الجانبية.<br />
        جميع العناصر متجاوبة وتعمل بسلاسة على الجوال.
      </p>

      {/* بطاقات الوصول السريع */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '2rem',
        marginBottom: '3rem'
      }}>
        <div
          onClick={() => navigate('/manager/orders')}
          style={{
            background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(76, 175, 80, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(76, 175, 80, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-clipboard-list" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>إدارة الطلبات</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            عرض ومتابعة جميع الطلبات مع إمكانية الطباعة والإدارة الكاملة
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/menu')}
          style={{
            background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(139, 69, 19, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(139, 69, 19, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(139, 69, 19, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-coffee" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>قائمة المشروبات</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            إدارة المشروبات والأسعار وإضافة عناصر جديدة للقائمة
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/categories')}
          style={{
            background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(255, 107, 53, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 107, 53, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 107, 53, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-tags" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>فئات المشروبات</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            إدارة تصنيفات المشروبات وإنشاء فئات جديدة مع الألوان المميزة
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/employees')}
          style={{
            background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(33, 150, 243, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(33, 150, 243, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(33, 150, 243, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-users" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>إدارة الموظفين</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            إضافة وإدارة حسابات الموظفين والصلاحيات
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/inventory')}
          style={{
            background: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(156, 39, 176, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(156, 39, 176, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(156, 39, 176, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-boxes" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>إدارة المخزون</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            متابعة المخزون والمواد الخام وإدارة المشتريات
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/tables')}
          style={{
            background: 'linear-gradient(135deg, #8E24AA 0%, #AB47BC 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(142, 36, 170, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(142, 36, 170, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(142, 36, 170, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-table" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>إدارة الطاولات</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            متابعة حسابات الطاولات النشطة وإدارة تصفية الحسابات
          </p>
        </div>

        <div
          onClick={() => navigate('/manager/reports')}
          style={{
            background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
            color: '#fff',
            padding: '2rem',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            boxShadow: '0 4px 12px rgba(255, 152, 0, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 152, 0, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 152, 0, 0.3)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <i className="fas fa-chart-bar" style={{ fontSize: '2rem' }}></i>
            <h3 style={{ margin: 0, fontSize: '1.3rem' }}>التقارير والإحصائيات</h3>
          </div>
          <p style={{ margin: 0, opacity: 0.9, lineHeight: '1.5' }}>
            عرض التقارير المالية وإحصائيات المبيعات والأداء
          </p>        </div>
      </div>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
