// API utilities for Coffee Shop Management System
// نظام إدارة المقهى - أدوات واجهة برمجة التطبيقات

import { getApiUrl, APP_CONFIG } from '../config/app.config';

const API_BASE_URL = APP_CONFIG.API.BASE_URL;
const REQUEST_TIMEOUT = APP_CONFIG.API.TIMEOUT;

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// إعداد الطلبات مع معالجة الأخطاء
const makeRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

  try {
    const token = localStorage.getItem('authToken');

    const response = await fetch(getApiUrl(endpoint), {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-request-id': Math.random().toString(36).substring(7),
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `خطأ HTTP ${response.status}`;
      try {
        const errorData = await response.clone().json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch {
        // استخدم الرسالة الافتراضية
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error: any) {
    clearTimeout(timeoutId);
    console.error('API Request Error:', error);
    return {
      success: false,
      error: error.message || 'حدث خطأ في الشبكة'
    };
  }
};

// Authentication API
export const loginUser = async (credentials: { username: string; password: string }) => {
  return makeRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(credentials),
  });
};

export const logoutUser = async () => {
  return makeRequest('/api/auth/logout', { method: 'POST' });
};

// Server Health Check
export const checkServerHealth = async () => {
  return makeRequest('/health');
};

// Users Management
export const getUsers = async () => {
  return makeRequest('/api/users');
};

export const createUser = async (userData: any) => {
  return makeRequest('/api/users', {
    method: 'POST',
    body: JSON.stringify(userData),
  });
};

export const updateUser = async (id: string, userData: any) => {
  return makeRequest(`/api/users/${id}`, {
    method: 'PUT',
    body: JSON.stringify(userData),
  });
};

export const deleteUser = async (id: string) => {
  return makeRequest(`/api/users/${id}`, { method: 'DELETE' });
};

// Menu Management
export const getMenuItems = async () => {
  return makeRequest('/api/menu');
};

export const createMenuItem = async (menuData: any) => {
  return makeRequest('/api/menu', {
    method: 'POST',
    body: JSON.stringify(menuData),
  });
};

export const updateMenuItem = async (id: string, menuData: any) => {
  return makeRequest(`/api/menu/${id}`, {
    method: 'PUT',
    body: JSON.stringify(menuData),
  });
};

export const deleteMenuItem = async (id: string) => {
  return makeRequest(`/api/menu/${id}`, { method: 'DELETE' });
};

// Categories Management
export const getCategories = async () => {
  return makeRequest('/api/categories');
};

export const createCategory = async (categoryData: any) => {
  return makeRequest('/api/categories', {
    method: 'POST',
    body: JSON.stringify(categoryData),
  });
};

export const updateCategory = async (id: string, categoryData: any) => {
  return makeRequest(`/api/categories/${id}`, {
    method: 'PUT',
    body: JSON.stringify(categoryData),
  });
};

export const deleteCategory = async (id: string) => {
  return makeRequest(`/api/categories/${id}`, { method: 'DELETE' });
};

// Orders Management
export const getOrders = async () => {
  return makeRequest('/api/orders');
};

export const createOrder = async (orderData: any) => {
  return makeRequest('/api/orders', {
    method: 'POST',
    body: JSON.stringify(orderData),
  });
};

export const updateOrder = async (id: string, orderData: any) => {
  return makeRequest(`/api/orders/${id}`, {
    method: 'PUT',
    body: JSON.stringify(orderData),
  });
};

export const updateOrderStatus = async (id: string, status: string) => {
  return makeRequest(`/api/orders/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify({ status }),
  });
};

export const deleteOrder = async (id: string) => {
  return makeRequest(`/api/orders/${id}`, { method: 'DELETE' });
};

// Inventory Management
export const getInventory = async () => {
  return makeRequest('/api/inventory');
};

export const updateInventory = async (id: string, inventoryData: any) => {
  return makeRequest(`/api/inventory/${id}`, {
    method: 'PUT',
    body: JSON.stringify(inventoryData),
  });
};

// Reports
export const getSalesReport = async (startDate?: string, endDate?: string) => {
  const params = new URLSearchParams();
  if (startDate) params.append('startDate', startDate);
  if (endDate) params.append('endDate', endDate);

  const query = params.toString();
  return makeRequest(`/api/reports/sales${query ? `?${query}` : ''}`);
};

export const getPopularItems = async () => {
  return makeRequest('/api/reports/popular-items');
};

// Shifts Management
export const getShifts = async () => {
  return makeRequest('/api/shifts');
};

export const createShift = async (shiftData: any) => {
  return makeRequest('/api/shifts', {
    method: 'POST',
    body: JSON.stringify(shiftData),
  });
};

export const updateShift = async (id: string, shiftData: any) => {
  return makeRequest(`/api/shifts/${id}`, {
    method: 'PUT',
    body: JSON.stringify(shiftData),
  });
};

export const deleteShift = async (id: string) => {
  return makeRequest(`/api/shifts/${id}`, { method: 'DELETE' });
};

// Default export for convenience
export default {
  // Auth
  loginUser,
  logoutUser,
  checkServerHealth,

  // Users
  getUsers,
  createUser,
  updateUser,
  deleteUser,

  // Menu
  getMenuItems,
  createMenuItem,
  updateMenuItem,
  deleteMenuItem,

  // Categories
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,

  // Orders
  getOrders,
  createOrder,
  updateOrder,
  updateOrderStatus,
  deleteOrder,

  // Inventory
  getInventory,
  updateInventory,

  // Reports
  getSalesReport,
  getPopularItems,

  // Shifts
  getShifts,
  createShift,
  updateShift,
  deleteShift,
};
