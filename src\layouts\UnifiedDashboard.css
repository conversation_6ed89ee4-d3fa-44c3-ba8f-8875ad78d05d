/* Unified Dashboard Styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #f39c12;
  --success-color: #27ae60;
  --error-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #3498db;
  --white: #ffffff;
  --light-bg: #f8f9fa;
  --border-color: #ecf0f1;
  --text-color: #2c3e50;
  --text-muted: #95a5a6;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 70px;
  --topbar-height: 70px;
}

.unified-dashboard {
  display: flex;
  min-height: 100vh;
  background: var(--light-bg);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading State */
.unified-dashboard.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color);
}

.loading-spinner i {
  font-size: 2.5rem;
  color: var(--primary-color);
}

.loading-spinner span {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background: var(--white);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar.closed {
  transform: translateX(-100%);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color);
}

.logo i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  display: none;
}

.sidebar-toggle:hover {
  background: var(--light-bg);
  color: var(--primary-color);
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.25rem;
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1rem;
}

.role {
  display: block;
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
}

.connection-status.connected {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.connection-status.disconnected {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: var(--text-color);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: right;
}

.nav-item:hover {
  background: var(--light-bg);
  color: var(--primary-color);
}

.nav-item.active {
  background: var(--primary-color);
  color: var(--white);
  border-right: 4px solid var(--secondary-color);
}

.nav-item i {
  width: 1.25rem;
  text-align: center;
  font-size: 1.1rem;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  border: none;
  background: var(--error-color);
  color: var(--white);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.logout-btn i {
  font-size: 1.1rem;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-right: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-right 0.3s ease;
}

.main-content.sidebar-closed {
  margin-right: 0;
}

/* Top Bar */
.top-bar {
  height: var(--topbar-height);
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.top-bar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
  background: var(--light-bg);
}

.screen-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.screen-title i {
  color: var(--secondary-color);
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.connection-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.connection-indicator.connected {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.connection-indicator.disconnected {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 500;
  color: var(--primary-color);
}

.logout-btn-mobile {
  background: var(--error-color);
  color: var(--white);
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.logout-btn-mobile:hover {
  background: #c0392b;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: var(--light-bg);
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-right: 0;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .mobile-overlay {
    display: block;
  }
  
  .top-bar {
    padding: 0 1rem;
  }
  
  .screen-title {
    font-size: 1.25rem;
  }
  
  .connection-indicator span {
    display: none;
  }
  
  .user-menu span {
    display: none;
  }
}

@media (max-width: 768px) {
  .top-bar {
    padding: 0 0.75rem;
  }
  
  .screen-title {
    font-size: 1.1rem;
  }
  
  .top-bar-right {
    gap: 0.75rem;
  }
  
  .sidebar {
    width: 100%;
  }
  
  .nav-item {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
  }
  
  .user-info {
    padding: 1.25rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .top-bar {
    padding: 0 0.5rem;
  }
  
  .screen-title {
    font-size: 1rem;
  }
  
  .screen-title span {
    display: none;
  }
  
  .connection-indicator {
    padding: 0.375rem 0.5rem;
  }
  
  .logout-btn-mobile {
    padding: 0.375rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #ecf0f1;
    --white: #2c3e50;
    --light-bg: #34495e;
    --border-color: #4a5f7a;
    --text-color: #ecf0f1;
    --text-muted: #bdc3c7;
  }
  
  .sidebar {
    background: #2c3e50;
  }
  
  .top-bar {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .nav-item:hover {
    background: #34495e;
  }
  
  .nav-item.active {
    background: #3498db;
  }
}
