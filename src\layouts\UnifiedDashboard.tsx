import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authenticatedGet } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
import socket from '../socket';

// Import Components
import DashboardHome from '../components/Dashboard/DashboardHome';
import OrdersScreen from '../components/Orders/OrdersScreen';
import ShiftManager from '../components/Shifts/ShiftManager';
import TablesManager from '../components/Tables/TablesManager';
import ReportsScreen from '../components/Reports/ReportsScreen';
import InventoryManager from '../components/Inventory/InventoryManager';
import MenuManager from '../components/Menu/MenuManager';
import CategoriesManager from '../components/Categories/CategoriesManager';

import './UnifiedDashboard.css';

interface User {
  _id: string;
  username: string;
  role: 'waiter' | 'chef' | 'manager';
  isActive: boolean;
}

const UnifiedDashboard: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [currentScreen, setCurrentScreen] = useState('home');
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useToast();

  // تحميل بيانات المستخدم
  const loadUserData = async () => {
    try {
      console.log('UnifiedDashboard - Loading user data...');
      const userData = localStorage.getItem('user');
      if (!userData) {
        navigate('/login');
        return;
      }

      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      
      // التحقق من صحة الجلسة
      const response = await authenticatedGet('/api/auth/verify');
      if (!response.success) {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        navigate('/login');
        return;
      }

    } catch (error) {
      console.error('خطأ في تحميل بيانات المستخدم:', error);
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  // إعداد Socket.IO
  useEffect(() => {
    if (socket) {
      socket.on('connect', () => {
        setIsConnected(true);
        console.log('🔗 متصل بالخادم - لوحة التحكم الموحدة');
      });

      socket.on('disconnect', () => {
        setIsConnected(false);
        console.log('❌ انقطع الاتصال - لوحة التحكم الموحدة');
      });

      // انضمام لغرفة المستخدم
      if (user) {
        socket.emit('join-user-room', {
          userId: user._id,
          username: user.username,
          role: user.role
        });
      }
    }

    return () => {
      if (socket) {
        socket.off('connect');
        socket.off('disconnect');
      }
    };
  }, [user]);

  // تحميل البيانات الأولية
  useEffect(() => {
    loadUserData();
  }, []);

  // معالجة تغيير حجم الشاشة
  useEffect(() => {
    const handleResize = () => {
      setSidebarOpen(window.innerWidth > 1024);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('waiterCart');
    localStorage.removeItem('waiterSession');
    
    if (socket) {
      socket.emit('user-logout', {
        userId: user?._id,
        username: user?.username
      });
    }
    
    showSuccess('تم تسجيل الخروج بنجاح');
    navigate('/login');
  };

  // قائمة الشاشات حسب الدور
  const getScreensForRole = (role: string) => {
    const commonScreens = [
      { id: 'home', name: 'الرئيسية', icon: 'fas fa-home' },
      { id: 'orders', name: 'الطلبات', icon: 'fas fa-shopping-cart' }
    ];

    const roleSpecificScreens = {
      waiter: [
        { id: 'tables', name: 'الطاولات', icon: 'fas fa-table' },
        { id: 'menu', name: 'قائمة المشروبات', icon: 'fas fa-coffee' }
      ],
      chef: [
        { id: 'inventory', name: 'المخزون', icon: 'fas fa-boxes' }
      ],
      manager: [
        { id: 'shifts', name: 'إدارة المناوبات', icon: 'fas fa-clock' },
        { id: 'tables', name: 'إدارة الطاولات', icon: 'fas fa-table' },
        { id: 'reports', name: 'التقارير', icon: 'fas fa-chart-bar' },
        { id: 'inventory', name: 'إدارة المخزون', icon: 'fas fa-boxes' },
        { id: 'menu', name: 'إدارة القائمة', icon: 'fas fa-coffee' },
        { id: 'categories', name: 'فئات المشروبات', icon: 'fas fa-tags' }
      ]
    };

    return [...commonScreens, ...(roleSpecificScreens[role as keyof typeof roleSpecificScreens] || [])];
  };

  // رندر المحتوى حسب الشاشة المحددة
  const renderContent = () => {
    if (!user) return null;

    const isManager = user.role === 'manager';

    switch (currentScreen) {
      case 'home':
        return <DashboardHome userRole={user.role} userName={user.username} />;
      case 'orders':
        return <OrdersScreen userRole={user.role} userName={user.username} />;
      case 'shifts':
        return isManager ? <ShiftManager /> : null;
      case 'tables':
        return <TablesManager userRole={user.role} userName={user.username} />;
      case 'reports':
        return isManager ? <ReportsScreen /> : null;
      case 'inventory':
        return <InventoryManager userRole={user.role} />;
      case 'menu':
        return <MenuManager userRole={user.role} />;
      case 'categories':
        return isManager ? <CategoriesManager /> : null;
      default:
        return <DashboardHome userRole={user.role} userName={user.username} />;
    }
  };

  // الحصول على عنوان الشاشة
  const getScreenTitle = () => {
    const screens = getScreensForRole(user?.role || '');
    const screen = screens.find(s => s.id === currentScreen);
    return screen?.name || 'لوحة التحكم';
  };

  if (loading) {
    return (
      <div className="unified-dashboard loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <span>جاري تحميل لوحة التحكم...</span>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="unified-dashboard">
      {/* Sidebar */}
      <div className={`sidebar ${sidebarOpen ? 'open' : 'closed'}`}>
        <div className="sidebar-header">
          <div className="logo">
            <i className="fas fa-coffee"></i>
            <span>ديشا كوفي</span>
          </div>
          <button 
            className="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <i className={`fas ${sidebarOpen ? 'fa-times' : 'fa-bars'}`}></i>
          </button>
        </div>

        <div className="user-info">
          <div className="user-avatar">
            <i className={`fas ${user.role === 'waiter' ? 'fa-user-tie' : user.role === 'chef' ? 'fa-chef-hat' : 'fa-user-cog'}`}></i>
          </div>
          <div className="user-details">
            <span className="username">{user.username}</span>
            <span className="role">
              {user.role === 'waiter' ? 'نادل' : user.role === 'chef' ? 'طباخ' : 'مدير'}
            </span>
          </div>
          <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
            <i className={`fas ${isConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
          </div>
        </div>

        <nav className="sidebar-nav">
          {getScreensForRole(user.role).map(screen => (
            <button
              key={screen.id}
              className={`nav-item ${currentScreen === screen.id ? 'active' : ''}`}
              onClick={() => setCurrentScreen(screen.id)}
            >
              <i className={screen.icon}></i>
              <span>{screen.name}</span>
            </button>
          ))}
        </nav>

        <div className="sidebar-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <i className="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className={`main-content ${sidebarOpen ? 'sidebar-open' : 'sidebar-closed'}`}>
        <div className="top-bar">
          <div className="top-bar-left">
            <button 
              className="mobile-menu-btn"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <i className="fas fa-bars"></i>
            </button>
            <h1 className="screen-title">
              <i className={getScreensForRole(user.role).find(s => s.id === currentScreen)?.icon || 'fas fa-home'}></i>
              {getScreenTitle()}
            </h1>
          </div>
          
          <div className="top-bar-right">
            <div className={`connection-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              <i className={`fas ${isConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
              <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
            </div>
            
            <div className="user-menu">
              <span>{user.username}</span>
              <button className="logout-btn-mobile" onClick={handleLogout}>
                <i className="fas fa-sign-out-alt"></i>
              </button>
            </div>
          </div>
        </div>

        <div className="content-area">
          {renderContent()}
        </div>
      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && window.innerWidth <= 1024 && (
        <div 
          className="mobile-overlay"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default UnifiedDashboard;
