/* Chef Dashboard Styles - Similar to Waiter Dashboard */

.chef-dashboard {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1001;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #333;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background: white;
  transform: scale(1.05);
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Sidebar */
.dashboard-sidebar {
  width: 300px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.dashboard-sidebar.hidden {
  display: none;
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.sidebar-logo i {
  font-size: 1.5rem;
  color: #f39c12;
}

.sidebar-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.sidebar-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  text-align: right;
  font-size: 1rem;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(-5px);
}

.nav-link.active {
  background: linear-gradient(90deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.nav-icon {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.nav-text {
  flex: 1;
}

/* Sidebar Stats */
.sidebar-stats {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #f39c12;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(243, 156, 18, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f39c12;
}

.stat-details {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.chef-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.chef-avatar {
  width: 50px;
  height: 50px;
  background: rgba(243, 156, 18, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f39c12;
  font-size: 1.5rem;
}

.chef-details {
  flex: 1;
}

.chef-name {
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
}

.chef-role {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(231, 76, 60, 0.2);
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: 8px;
  color: #e74c3c;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(231, 76, 60, 0.3);
  transform: translateY(-2px);
}

/* Main Content */
.dashboard-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.content-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 4rem);
  padding: 2rem;
}

/* Content Container */
.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Screen Header */
.screen-header {
  text-align: center;
  margin-bottom: 2rem;
}

.screen-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.screen-title i {
  color: #f39c12;
}

.screen-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* Orders Stats */
.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.stat-card .stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-card .stat-content {
  flex: 1;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-card .stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.loading-spinner {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #f39c12;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #bdc3c7;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.empty-state p {
  font-size: 1rem;
  margin: 0;
}

/* Orders Grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Order Card */
.order-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 5px solid #f39c12;
}

.order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left-color: #f39c12;
}

.order-card.preparing {
  border-left-color: #e74c3c;
}

.order-card.ready {
  border-left-color: #27ae60;
}

/* Order Header */
.order-header {
  padding: 1.5rem 1.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ecf0f1;
}

.order-number {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-status.pending {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.order-status.preparing {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.order-status.ready {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

/* Order Info */
.order-info {
  padding: 1rem 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.info-row .value {
  font-weight: 600;
  color: #2c3e50;
}

/* Order Items */
.order-items {
  padding: 1rem 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.order-items h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
}

.item-name {
  color: #2c3e50;
}

.item-quantity {
  color: #f39c12;
  font-weight: 600;
}

.more-items {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.875rem;
  font-style: italic;
  padding: 0.5rem;
}

/* Order Actions */
.order-actions {
  padding: 1rem 1.5rem;
  border-top: 1px solid #ecf0f1;
  display: flex;
  gap: 0.75rem;
}

.order-actions button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-details {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-details:hover {
  background: #d5dbdb;
  transform: translateY(-2px);
}

.btn-accept {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.btn-accept:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
}

.btn-complete {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-complete:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.modal-body {
  padding: 1.5rem;
}

.order-info-section,
.order-items-section {
  margin-bottom: 2rem;
}

.order-info-section h3,
.order-items-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item .label {
  font-size: 0.875rem;
  color: #7f8c8d;
  font-weight: 600;
}

.info-item .value {
  font-weight: 600;
  color: #2c3e50;
}

.total-amount {
  font-size: 1.25rem;
  color: #27ae60 !important;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-badge.pending {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.status-badge.preparing {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.status-badge.ready {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.item-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 0.75rem;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.item-notes {
  font-size: 0.875rem;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 600;
}

.item-quantity {
  color: #f39c12;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.item-price {
  color: #7f8c8d;
}

.item-total {
  color: #27ae60;
  font-size: 1.1rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #ecf0f1;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.modal-footer button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-close {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-close:hover {
  background: #d5dbdb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-sidebar {
    position: fixed;
    top: 0;
    right: -100vw;
    height: 100vh;
    z-index: 1000;
    transition: right 0.3s ease;
  }

  .dashboard-sidebar.visible {
    right: 0;
  }

  .dashboard-main {
    padding: 1rem;
    margin-right: 0;
  }

  .content-card {
    padding: 1rem;
    border-radius: 15px;
  }

  .screen-title {
    font-size: 2rem;
  }

  .orders-grid {
    grid-template-columns: 1fr;
  }

  .order-actions {
    flex-direction: column;
  }

  .orders-stats {
    grid-template-columns: 1fr;
  }

  .order-info-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .item-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .item-details {
    align-self: flex-end;
  }
}
