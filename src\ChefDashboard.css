/* ChefDashboard Styles */
.chef-dashboard {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

.chef-main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: transparent;
}

/* Header Section */
.chef-header {
  background: linear-gradient(135deg, #6d4c41 0%, #8d6e63 100%);
  color: white;
  padding: 30px;
  border-radius: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(109, 76, 65, 0.3);
  position: relative;
  overflow: hidden;
}

.chef-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.chef-header-content {
  position: relative;
  z-index: 1;
}

.chef-title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.chef-welcome {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* Navigation Tabs */
.chef-tabs {
  background: white;
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.chef-tab-button {
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-align: center;
}

.chef-tab-button.active {
  background: linear-gradient(135deg, #6d4c41 0%, #8d6e63 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(109, 76, 65, 0.4);
}

.chef-tab-button:not(.active) {
  background: #f8f9fa;
  color: #6d4c41;
  border: 2px solid #e9ecef;
}

.chef-tab-button:not(.active):hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.tab-count {
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9rem;
  margin-right: 8px;
  font-weight: 700;
}

.chef-tab-button:not(.active) .tab-count {
  background: #6d4c41;
  color: white;
}

/* Orders Grid */
.chef-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Order Card */
.chef-order-card {
  background: white;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.chef-order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 45px rgba(0,0,0,0.15);
}

.chef-order-card.pending {
  border-color: #ff9800;
  background: linear-gradient(145deg, #fff8e1 0%, #ffffff 100%);
}

.chef-order-card.preparing {
  border-color: #2196f3;
  background: linear-gradient(145deg, #e3f2fd 0%, #ffffff 100%);
}

.chef-order-card.completed {
  border-color: #4caf50;
  background: linear-gradient(145deg, #e8f5e8 0%, #ffffff 100%);
}

/* Order Header */
.chef-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.chef-order-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #6d4c41;
  margin: 0;
}

.chef-order-status {
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  color: white;
}

.status-preparing {
  background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
  color: white;
}

.status-ready {
  background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);
  color: white;
}

.status-delivered {
  background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
  color: white;
}

/* Order Info */
.chef-order-info {
  margin-bottom: 20px;
}

.chef-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.chef-info-row:last-child {
  border-bottom: none;
}

.chef-info-label {
  font-weight: 600;
  color: #666;
  font-size: 0.95rem;
}

.chef-info-value {
  font-weight: 500;
  color: #333;
  font-size: 0.95rem;
}

/* Order Items */
.chef-order-items {
  margin-bottom: 20px;
}

.chef-items-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #6d4c41;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chef-items-title::before {
  content: '🍽️';
  font-size: 1.2rem;
}

.chef-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.chef-item:hover {
  background: #e9ecef;
  transform: translateX(-5px);
}

.chef-item-info {
  flex: 1;
}

.chef-item-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 1rem;
}

.chef-item-details {
  font-size: 0.9rem;
  color: #666;
}

.chef-item-notes {
  font-size: 0.85rem;
  color: #ff9800;
  font-style: italic;
  margin-top: 4px;
}

.chef-item-quantity {
  background: #6d4c41;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: center;
}

/* Order Actions */
.chef-order-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.chef-action-button {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chef-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.chef-action-button:active {
  transform: translateY(0);
}

.accept-button {
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  color: white;
}

.ready-button {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  color: white;
}

.cancel-button {
  background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
  color: white;
}

/* Empty State */
.chef-empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.chef-empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.chef-empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #666;
  margin-bottom: 10px;
}

.chef-empty-subtitle {
  font-size: 1rem;
  color: #999;
  margin: 0;
}

/* Stats Cards */
.chef-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chef-stat-card {
  background: white;
  padding: 25px;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.chef-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6d4c41 0%, #8d6e63 100%);
}

.chef-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #6d4c41;
  margin-bottom: 10px;
}

.chef-stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

/* Loading State */
.chef-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.chef-loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6d4c41;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chef-loading-text {
  font-size: 1.2rem;
  color: #666;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chef-dashboard {
    flex-direction: column;
  }
  
  .chef-main-content {
    padding: 15px;
  }
  
  .chef-header {
    padding: 20px;
    margin-bottom: 15px;
  }
  
  .chef-title {
    font-size: 2rem;
  }
  
  .chef-tabs {
    padding: 10px;
    gap: 10px;
  }
  
  .chef-tab-button {
    padding: 12px 20px;
    font-size: 1rem;
    min-width: 100px;
  }
  
  .chef-orders-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .chef-order-card {
    padding: 20px;
  }
  
  .chef-order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .chef-order-actions {
    flex-direction: column;
  }
  
  .chef-action-button {
    min-width: auto;
  }
  
  .chef-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .chef-stat-card {
    padding: 20px;
  }
  
  .chef-stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .chef-main-content {
    padding: 10px;
  }
  
  .chef-header {
    padding: 15px;
  }
  
  .chef-title {
    font-size: 1.5rem;
  }
  
  .chef-welcome {
    font-size: 1rem;
  }
  
  .chef-order-card {
    padding: 15px;
  }
  
  .chef-order-number {
    font-size: 1.2rem;
  }
  
  .chef-tab-button {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chef-order-card {
  animation: fadeInUp 0.6s ease-out;
}

.chef-order-card:nth-child(1) { animation-delay: 0.1s; }
.chef-order-card:nth-child(2) { animation-delay: 0.2s; }
.chef-order-card:nth-child(3) { animation-delay: 0.3s; }
.chef-order-card:nth-child(4) { animation-delay: 0.4s; }

/* Custom Scrollbar */
.chef-main-content::-webkit-scrollbar {
  width: 8px;
}

.chef-main-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.chef-main-content::-webkit-scrollbar-thumb {
  background: rgba(109, 76, 65, 0.3);
  border-radius: 4px;
}

.chef-main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(109, 76, 65, 0.5);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 9999;
  direction: rtl;
}

/* Additional utility classes */
.chef-badge {
  display: inline-block;
  padding: 4px 12px;
  background: #6d4c41;
  color: white;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.chef-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  margin: 20px 0;
}

.chef-highlight {
  background: linear-gradient(120deg, transparent 0%, rgba(109, 76, 65, 0.1) 50%, transparent 100%);
  padding: 2px 8px;
  border-radius: 6px;
}
