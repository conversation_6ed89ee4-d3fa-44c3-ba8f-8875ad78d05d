import React, { useState, useEffect } from 'react';
import socket from './socket.ts';
import EnhancedOrdersTable from './components/EnhancedOrdersTable';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import { authenticatedGet, authenticatedPut, handleApiError } from './utils/apiHelpers';
import type { Order } from './types/Order';
import { getOrderTotal, formatOrderPrice } from './types/Order';

// تم نقل interface Order إلى src/types/Order.ts

interface DiscountRequest {
  _id: string;
  orderId: Order;
  waiterName: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  managerName?: string;
  managerNotes?: string;
  createdAt: string;
  processedAt?: string;
}

export default function ManagerOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [discountRequests, setDiscountRequests] = useState<DiscountRequest[]>([]);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedDiscountRequest, setSelectedDiscountRequest] = useState<DiscountRequest | null>(null);
  const [managerNotes, setManagerNotes] = useState('');
  const { toasts, removeToast, showSuccess, showError } = useToast();  // جلب الطلبات من السيرفر عند التحميل والتحديث كل 3 ثوانٍ
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const data = await authenticatedGet('/api/orders');
        setOrders(data);
      } catch (error) {
        console.error('Error fetching orders:', error);
        showError(handleApiError(error));
      }
    };

    const fetchDiscountRequests = async () => {
      try {
        const data = await authenticatedGet('/api/discount-requests?status=pending');
        setDiscountRequests(data);
      } catch (error) {
        console.error('Error fetching discount requests:', error);
        showError(handleApiError(error));
      }
    };

    fetchOrders();
    fetchDiscountRequests();

    // استقبال التحديثات اللحظية من السيرفر
    socket.emit('register', 'مدير');
    socket.on('orderUpdate', fetchOrders);
    socket.on('newOrder', fetchOrders);
    socket.on('newDiscountRequest', fetchDiscountRequests);

    return () => {
      socket.off('orderUpdate', fetchOrders);
      socket.off('newOrder', fetchOrders);
      socket.off('newDiscountRequest', fetchDiscountRequests);
    };
  }, [showError]);

  // تسجيل المدير في Socket.IO (مرة واحدة فقط)
  useEffect(() => {
    socket.emit('register', { role: 'مدير', name: localStorage.getItem('username') || 'مدير' });

    socket.on('registered', (data: any) => {
      console.log('✅ تم تسجيل المدير في Socket.IO:', data);
    });

    return () => {
      socket.off('registered');
    };
  }, []);

  // تحديث حالة الطلب
  const handleStatusChange = async (orderId: string, newStatus: Order['status']) => {
    setLoading(true);
    try {
      await authenticatedPut(`/api/orders/${orderId}`, { status: newStatus });

      setOrders(orders =>
        orders.map(order =>
          order._id === orderId
            ? { ...order, status: newStatus, updatedAt: new Date().toISOString() } as Order
            : order
        )
      );
      showSuccess('تم تحديث حالة الطلب بنجاح');
    } catch (error) {
      console.error('Error updating order status:', error);
      showError(handleApiError(error));
    }
    setLoading(false);
  };
  // معالجة طلبات الخصم
  const handleDiscountRequest = async (requestId: string, status: 'approved' | 'rejected') => {
    setLoading(true);
    try {
      const response = await authenticatedPut(`/api/discount-requests/${requestId}`, {
        status,
        managerName: localStorage.getItem('username') || 'مدير',
        managerNotes
      });

      setDiscountRequests(requests =>
        requests.filter(req => req._id !== requestId)
      );

      // تحديث الطلبات لإظهار الخصم المطبق
      if (status === 'approved') {
        const updatedOrders = await authenticatedGet('/api/orders');
        setOrders(updatedOrders);
      }

      // الإشعار يتم إرساله تلقائياً من الخادم، لا حاجة لإرساله مرة أخرى

      showSuccess(status === 'approved' ? 'تم الموافقة على طلب الخصم' : 'تم رفض طلب الخصم');
      setShowDiscountModal(false);
      setSelectedDiscountRequest(null);
      setManagerNotes('');
    } catch (error) {
      showError(handleApiError(error));
    }
    setLoading(false);
  };





  return (
    <div className="manager-orders-container" style={{
      direction: 'rtl',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background:
          `radial-gradient(circle at 20% 80%, rgba(109, 76, 65, 0.1) 0%, transparent 50%),
           radial-gradient(circle at 80% 20%, rgba(255, 171, 64, 0.1) 0%, transparent 50%)`,
        pointerEvents: 'none',
        zIndex: -1
      }}></div>

      {/* Enhanced Header */}
      <div style={{
        background: 'linear-gradient(135deg, #6d4c41 0%, #8d6e63 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '24px',
        boxShadow: '0 8px 32px rgba(109, 76, 65, 0.2)',
        marginBottom: '2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Header Background Pattern */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', marginBottom: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '16px',
              backdropFilter: 'blur(10px)'
            }}>
              <i className="fas fa-clipboard-list" style={{ fontSize: '2rem', color: '#ffab40' }}></i>
            </div>
            <div>
              <h1 style={{ margin: 0, fontSize: '2.5rem', fontWeight: '700' }}>
                إدارة الطلبات المحسنة
              </h1>
              <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9, fontSize: '1.1rem' }}>
                لوحة تحكم شاملة لإدارة جميع الطلبات مع تحليلات متقدمة
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginTop: '1.5rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-list-alt" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>{orders.length}</div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>إجمالي الطلبات</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-clock" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => o.status === 'pending').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>في الانتظار</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-utensils" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => o.status === 'preparing').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>قيد التحضير</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-check-circle" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.filter(o => o.status === 'delivered').length}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>مكتملة</div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <i className="fas fa-money-bill-wave" style={{ fontSize: '1.5rem', color: '#ffab40', marginBottom: '0.5rem' }}></i>
              <div style={{ fontSize: '1.8rem', fontWeight: '700' }}>
                {orders.reduce((sum, order) => sum + getOrderTotal(order), 0).toFixed(2)}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>إجمالي المبيعات (ج.م)</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '24px',
        padding: '2rem',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(109, 76, 65, 0.1)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Content Top Border */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, #6d4c41, #ffab40)'
        }}></div>

        {/* Enhanced Discount Requests Section */}
        {discountRequests.length > 0 && (
          <div style={{
            background: 'linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%)',
            border: '2px solid rgba(255, 152, 0, 0.3)',
            borderRadius: '20px',
            padding: '2rem',
            marginBottom: '2rem',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 8px 25px rgba(255, 152, 0, 0.15)'
          }}>
            {/* Alert Icon Background */}
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              background: 'rgba(255, 152, 0, 0.1)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <i className="fas fa-bell" style={{ fontSize: '2rem', color: 'rgba(255, 152, 0, 0.3)' }}></i>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '1.5rem',
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #ff9800, #ffc107)',
                padding: '0.75rem',
                borderRadius: '12px',
                color: 'white',
                animation: 'pulse 2s ease-in-out infinite'
              }}>
                <i className="fas fa-percentage" style={{ fontSize: '1.5rem' }}></i>
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  color: '#e65100',
                  fontSize: '1.5rem',
                  fontWeight: '700'
                }}>
                  طلبات خصم تحتاج موافقة
                </h3>
                <p style={{
                  margin: '0.25rem 0 0 0',
                  color: '#bf360c',
                  fontSize: '1rem'
                }}>
                  {discountRequests.length} طلب في انتظار المراجعة
                </p>
              </div>
            </div>

            <div style={{ display: 'grid', gap: '1.5rem', gridTemplateColumns: 'repeat(auto-fit, minmax(380px, 1fr))' }}>
              {discountRequests.map(request => (
                <div key={request._id} style={{
                  background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                  border: '2px solid rgba(255, 152, 0, 0.2)',
                  borderRadius: '16px',
                  padding: '1.5rem',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
                }}>
                  {/* Card Top Border */}
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '3px',
                    background: 'linear-gradient(90deg, #ff9800, #ffc107)'
                  }}></div>

                  {/* Request Badge */}
                  <div style={{
                    position: 'absolute',
                    top: '1rem',
                    left: '1rem',
                    background: 'linear-gradient(135deg, #ff9800, #ffc107)',
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem',
                    fontWeight: '600'
                  }}>
                    طلب خصم
                  </div>

                  <div style={{ paddingLeft: '5rem' }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '1.5rem'
                    }}>
                      <div>
                        <h4 style={{
                          margin: '0 0 0.5rem 0',
                          color: '#6d4c41',
                          fontSize: '1.2rem',
                          fontWeight: '700'
                        }}>
                          طلب رقم: {request.orderId.orderNumber}
                        </h4>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          color: '#666',
                          fontSize: '0.9rem'
                        }}>
                          <i className="fas fa-user" style={{ color: '#ff9800' }}></i>
                          النادل: <strong>{request.waiterName}</strong>
                        </div>
                      </div>
                      <div style={{
                        textAlign: 'left',
                        background: 'rgba(255, 152, 0, 0.1)',
                        padding: '0.5rem 1rem',
                        borderRadius: '12px'
                      }}>
                        <div style={{
                          fontSize: '0.8rem',
                          color: '#ff9800',
                          fontWeight: '600'
                        }}>
                          <i className="fas fa-clock" style={{ marginLeft: '0.25rem' }}></i>
                          {new Date(request.createdAt).toLocaleString('ar-EG', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Details Grid */}
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                      gap: '1rem',
                      marginBottom: '1.5rem'
                    }}>
                      <div style={{
                        background: 'linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%)',
                        padding: '1rem',
                        borderRadius: '12px',
                        border: '1px solid rgba(76, 175, 80, 0.2)',
                        textAlign: 'center'
                      }}>
                        <div style={{
                          color: '#4caf50',
                          fontSize: '0.8rem',
                          fontWeight: '600',
                          marginBottom: '0.5rem'
                        }}>
                          <i className="fas fa-money-bill-wave" style={{ marginLeft: '0.25rem' }}></i>
                          إجمالي الطلب
                        </div>
                        <div style={{
                          fontSize: '1.2rem',
                          fontWeight: '700',
                          color: '#2e7d32'
                        }}>
                          {formatOrderPrice(request.orderId, 'ج.م')}
                        </div>
                      </div>

                      <div style={{
                        background: 'linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%)',
                        padding: '1rem',
                        borderRadius: '12px',
                        border: '1px solid rgba(255, 152, 0, 0.2)',
                        textAlign: 'center'
                      }}>
                        <div style={{
                          color: '#ff9800',
                          fontSize: '0.8rem',
                          fontWeight: '600',
                          marginBottom: '0.5rem'
                        }}>
                          <i className="fas fa-percentage" style={{ marginLeft: '0.25rem' }}></i>
                          الخصم المطلوب
                        </div>
                        <div style={{
                          fontSize: '1.2rem',
                          fontWeight: '700',
                          color: '#d84315'
                        }}>
                          {request.discountType === 'percentage'
                            ? `${request.discountValue}%`
                            : `${request.discountValue} ج.م`}
                        </div>
                      </div>

                      <div style={{
                        background: 'linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%)',
                        padding: '1rem',
                        borderRadius: '12px',
                        border: '1px solid rgba(33, 150, 243, 0.2)',
                        textAlign: 'center'
                      }}>
                        <div style={{
                          color: '#2196f3',
                          fontSize: '0.8rem',
                          fontWeight: '600',
                          marginBottom: '0.5rem'
                        }}>
                          <i className="fas fa-calculator" style={{ marginLeft: '0.25rem' }}></i>
                          المبلغ النهائي
                        </div>
                        <div style={{
                          fontSize: '1.2rem',
                          fontWeight: '700',
                          color: '#1976d2'
                        }}>
                          {(() => {
                            const totalPrice = getOrderTotal(request.orderId);
                            return request.discountType === 'percentage'
                              ? (totalPrice - (totalPrice * request.discountValue / 100)).toFixed(2)
                              : (totalPrice - request.discountValue).toFixed(2);
                          })()} ج.م
                        </div>
                      </div>
                    </div>

                    {/* Reason Section */}
                    <div style={{
                      background: 'linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%)',
                      padding: '1rem',
                      borderRadius: '12px',
                      border: '1px solid rgba(156, 39, 176, 0.2)',
                      marginBottom: '1.5rem'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        marginBottom: '0.5rem'
                      }}>
                        <i className="fas fa-comment-alt" style={{ color: '#9c27b0' }}></i>
                        <span style={{ color: '#9c27b0', fontWeight: '600', fontSize: '0.9rem' }}>سبب طلب الخصم:</span>
                      </div>
                      <p style={{
                        margin: 0,
                        color: '#6a1b9a',
                        fontSize: '1rem',
                        lineHeight: '1.5',
                        fontStyle: 'italic'
                      }}>
                        "{request.reason}"
                      </p>
                    </div>

                    {/* Enhanced Action Buttons */}
                    <div style={{ display: 'flex', gap: '0.75rem' }}>
                      <button
                        onClick={() => {
                          setSelectedDiscountRequest(request);
                          setShowDiscountModal(true);
                        }}
                        style={{
                          flex: 1,
                          background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                          color: 'white',
                          border: 'none',
                          padding: '1rem',
                          borderRadius: '12px',
                          cursor: 'pointer',
                          fontSize: '1rem',
                          fontWeight: '600',
                          transition: 'all 0.3s ease',
                          boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '0.5rem'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 6px 20px rgba(76, 175, 80, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(76, 175, 80, 0.3)';
                        }}
                      >
                        <i className="fas fa-check"></i>
                        الموافقة
                      </button>
                      <button
                        onClick={() => handleDiscountRequest(request._id, 'rejected')}
                        disabled={loading}
                        style={{
                          flex: 1,
                          background: loading ? '#ccc' : 'linear-gradient(135deg, #f44336 0%, #e57373 100%)',
                          color: 'white',
                          border: 'none',
                          padding: '1rem',
                          borderRadius: '12px',
                          cursor: loading ? 'not-allowed' : 'pointer',
                          fontSize: '1rem',
                          fontWeight: '600',
                          transition: 'all 0.3s ease',
                          boxShadow: loading ? 'none' : '0 4px 12px rgba(244, 67, 54, 0.3)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '0.5rem',
                          opacity: loading ? 0.7 : 1
                        }}
                        onMouseEnter={(e) => {
                          if (!loading) {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 6px 20px rgba(244, 67, 54, 0.4)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!loading) {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(244, 67, 54, 0.3)';
                          }
                        }}
                      >
                        <i className="fas fa-times"></i>
                        {loading ? 'جاري المعالجة...' : 'رفض'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <EnhancedOrdersTable
          orders={orders}
          userRole="manager"
          onStatusChange={handleStatusChange}
          loading={loading}
        />      </div>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* Enhanced Discount Approval Modal */}
      {showDiscountModal && selectedDiscountRequest && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.8)',
          backdropFilter: 'blur(15px)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000,
          direction: 'rtl',
          animation: 'fadeIn 0.3s ease-out'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
            padding: '0',
            borderRadius: '24px',
            boxShadow: '0 25px 80px rgba(0,0,0,0.2), 0 15px 40px rgba(109,76,65,0.15)',
            maxWidth: '600px',
            width: '95%',
            maxHeight: '90vh',
            overflowY: 'auto',
            border: '1px solid rgba(109,76,65,0.1)',
            position: 'relative',
            animation: 'slideUp 0.4s ease-out'
          }}>
            {/* Enhanced Modal Header */}
            <div style={{
              background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',
              color: 'white',
              padding: '2rem',
              borderRadius: '24px 24px 0 0',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* Header Background Pattern */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                opacity: 0.3
              }}></div>

              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                  <div style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    padding: '1rem',
                    borderRadius: '16px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <i className="fas fa-percentage" style={{ fontSize: '1.8rem', color: 'white' }}></i>
                  </div>
                  <div>
                    <h2 style={{ margin: 0, fontSize: '1.8rem', fontWeight: '700' }}>
                      الموافقة على طلب الخصم
                    </h2>
                    <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9, fontSize: '1rem' }}>
                      طلب رقم {selectedDiscountRequest.orderId.orderNumber}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Modal Body */}
            <div style={{ padding: '2rem' }}>
              {/* Request Information Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  background: 'linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%)',
                  padding: '1.5rem',
                  borderRadius: '16px',
                  border: '1px solid rgba(33,150,243,0.2)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    color: '#1976d2',
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    <i className="fas fa-user" style={{ marginLeft: '0.5rem' }}></i>
                    النادل
                  </div>
                  <div style={{
                    fontSize: '1.2rem',
                    fontWeight: '700',
                    color: '#2e7d32'
                  }}>
                    {selectedDiscountRequest.waiterName}
                  </div>
                </div>

                <div style={{
                  background: 'linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%)',
                  padding: '1.5rem',
                  borderRadius: '16px',
                  border: '1px solid rgba(76,175,80,0.2)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    color: '#4caf50',
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    <i className="fas fa-money-bill-wave" style={{ marginLeft: '0.5rem' }}></i>
                    إجمالي الطلب
                  </div>
                  <div style={{
                    fontSize: '1.4rem',
                    fontWeight: '700',
                    color: '#2e7d32'
                  }}>
                    {selectedDiscountRequest.orderId && typeof selectedDiscountRequest.orderId === 'object' && 'totalPrice' in selectedDiscountRequest.orderId && typeof selectedDiscountRequest.orderId.totalPrice === 'number' ? selectedDiscountRequest.orderId.totalPrice : selectedDiscountRequest.orderId?.totals?.total || 0} ج.م
                  </div>
                </div>

                <div style={{
                  background: 'linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%)',
                  padding: '1.5rem',
                  borderRadius: '16px',
                  border: '1px solid rgba(255,152,0,0.2)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    color: '#ff9800',
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    <i className="fas fa-percentage" style={{ marginLeft: '0.5rem' }}></i>
                    الخصم المطلوب
                  </div>
                  <div style={{
                    fontSize: '1.2rem',
                    fontWeight: '700',
                    color: '#d84315'
                  }}>
                    {selectedDiscountRequest.discountType === 'percentage'
                      ? `${selectedDiscountRequest.discountValue}%`
                      : `${selectedDiscountRequest.discountValue} ج.م`}
                  </div>
                  <div style={{
                    fontSize: '0.8rem',
                    color: '#bf360c',
                    marginTop: '0.25rem'
                  }}>
                    ({selectedDiscountRequest.discountType === 'percentage'
                      ? (getOrderTotal(selectedDiscountRequest.orderId) * selectedDiscountRequest.discountValue / 100).toFixed(2)
                      : selectedDiscountRequest.discountValue} ج.م)
                  </div>
                </div>

                <div style={{
                  background: 'linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%)',
                  padding: '1.5rem',
                  borderRadius: '16px',
                  border: '1px solid rgba(156,39,176,0.2)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    color: '#9c27b0',
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    <i className="fas fa-calculator" style={{ marginLeft: '0.5rem' }}></i>
                    المبلغ النهائي
                  </div>
                  <div style={{
                    fontSize: '1.4rem',
                    fontWeight: '700',
                    color: '#6a1b9a'
                  }}>
                    {(() => {
                      const totalPrice = getOrderTotal(selectedDiscountRequest.orderId);
                      return selectedDiscountRequest.discountType === 'percentage'
                        ? (totalPrice - (totalPrice * selectedDiscountRequest.discountValue / 100)).toFixed(2)
                        : (totalPrice - selectedDiscountRequest.discountValue).toFixed(2);
                    })()} ج.م
                  </div>
                </div>
              </div>

              {/* Reason Section */}
              <div style={{
                background: 'linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%)',
                borderRadius: '16px',
                padding: '1.5rem',
                border: '1px solid rgba(255, 152, 0, 0.2)',
                marginBottom: '2rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    background: 'linear-gradient(135deg, #ff9800, #ffc107)',
                    padding: '0.5rem',
                    borderRadius: '8px',
                    color: 'white'
                  }}>
                    <i className="fas fa-comment-alt"></i>
                  </div>
                  <h4 style={{ margin: 0, color: '#e65100', fontSize: '1.1rem', fontWeight: '700' }}>
                    سبب طلب الخصم:
                  </h4>
                </div>
                <p style={{
                  margin: 0,
                  color: '#bf360c',
                  fontSize: '1.1rem',
                  lineHeight: '1.6',
                  fontStyle: 'italic',
                  background: 'rgba(255, 255, 255, 0.7)',
                  padding: '1rem',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 152, 0, 0.3)'
                }}>
                  "{selectedDiscountRequest.reason}"
                </p>
              </div>

              {/* Enhanced Manager Notes Section */}
              <div style={{
                background: 'linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%)',
                borderRadius: '16px',
                padding: '1.5rem',
                border: '1px solid rgba(156, 39, 176, 0.2)',
                marginBottom: '2rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    background: 'linear-gradient(135deg, #9c27b0, #ba68c8)',
                    padding: '0.5rem',
                    borderRadius: '8px',
                    color: 'white'
                  }}>
                    <i className="fas fa-sticky-note"></i>
                  </div>
                  <h4 style={{ margin: 0, color: '#6a1b9a', fontSize: '1.1rem', fontWeight: '700' }}>
                    ملاحظات المدير (اختياري):
                  </h4>
                </div>
                <textarea
                  value={managerNotes}
                  onChange={(e) => setManagerNotes(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '1rem',
                    border: '2px solid rgba(156, 39, 176, 0.2)',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    minHeight: '100px',
                    resize: 'vertical',
                    fontFamily: 'inherit',
                    background: 'rgba(255, 255, 255, 0.8)',
                    transition: 'all 0.3s ease',
                    outline: 'none'
                  }}
                  placeholder="أضف أي ملاحظات أو تعليقات للنادل (اختياري)"
                  onFocus={(e) => {
                    e.target.style.borderColor = '#9c27b0';
                    e.target.style.boxShadow = '0 0 0 3px rgba(156, 39, 176, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = 'rgba(156, 39, 176, 0.2)';
                    e.target.style.boxShadow = 'none';
                  }}
                />
              </div>

              {/* Enhanced Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'flex-end',
                flexDirection: 'row-reverse'
              }}>
                <button
                  onClick={() => handleDiscountRequest(selectedDiscountRequest._id, 'approved')}
                  disabled={loading}
                  style={{
                    flex: 1,
                    background: loading ? '#ccc' : 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                    color: 'white',
                    border: 'none',
                    padding: '1rem 2rem',
                    borderRadius: '16px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    transition: 'all 0.3s ease',
                    boxShadow: loading ? 'none' : '0 4px 12px rgba(76, 175, 80, 0.3)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem',
                    opacity: loading ? 0.7 : 1
                  }}
                  onMouseEnter={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 6px 20px rgba(76, 175, 80, 0.4)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(76, 175, 80, 0.3)';
                    }
                  }}
                >
                  <i className="fas fa-check-circle"></i>
                  {loading ? 'جاري المعالجة...' : 'الموافقة وتطبيق الخصم'}
                </button>

                <button
                  onClick={() => handleDiscountRequest(selectedDiscountRequest._id, 'rejected')}
                  disabled={loading}
                  style={{
                    flex: 1,
                    background: loading ? '#ccc' : 'linear-gradient(135deg, #f44336 0%, #e57373 100%)',
                    color: 'white',
                    border: 'none',
                    padding: '1rem 2rem',
                    borderRadius: '16px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    transition: 'all 0.3s ease',
                    boxShadow: loading ? 'none' : '0 4px 12px rgba(244, 67, 54, 0.3)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem',
                    opacity: loading ? 0.7 : 1
                  }}
                  onMouseEnter={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 6px 20px rgba(244, 67, 54, 0.4)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(244, 67, 54, 0.3)';
                    }
                  }}
                >
                  <i className="fas fa-times-circle"></i>
                  {loading ? 'جاري المعالجة...' : 'رفض الطلب'}
                </button>

                <button
                  onClick={() => {
                    setShowDiscountModal(false);
                    setSelectedDiscountRequest(null);
                    setManagerNotes('');
                  }}
                  style={{
                    flex: 1,
                    background: 'linear-gradient(135deg, #757575 0%, #9e9e9e 100%)',
                    color: 'white',
                    border: 'none',
                    padding: '1rem 2rem',
                    borderRadius: '16px',
                    cursor: 'pointer',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 12px rgba(117, 117, 117, 0.3)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 20px rgba(117, 117, 117, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(117, 117, 117, 0.3)';
                  }}
                >
                  <i className="fas fa-ban"></i>
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
