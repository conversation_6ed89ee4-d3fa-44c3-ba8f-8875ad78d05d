/* تصميم صفحة المدير */
.manager-layout {
  display: flex;
  min-height: 100vh;
  background: var(--background);
  direction: rtl;
}

/* تصميم القائمة الجانبية */
.sidebar {
  background: var(--primary);
  color: white;
  min-width: 240px;
  padding: var(--spacing-lg) var(--spacing-md);
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 100vh;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.sidebar-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.sidebar-theme-toggle {
  position: absolute;
  top: 0;
  left: 0;
}

.sidebar-logo {
  width: 80px;
  height: 80px;
  margin-bottom: var(--spacing-sm);
}

.sidebar-header h3 {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  margin: 0;
  color: white;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.sidebar-btn {
  background: var(--primary-dark);
  color: white;
  border: none;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  text-align: right;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all 0.2s;
}

.sidebar-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-5px);
}

.sidebar-btn.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-sm);
}

.sidebar-btn .icon {
  font-size: var(--font-size-xl);
}

.sidebar-footer {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.logout-sidebar-btn {
  background: var(--accent);
  color: white;
  border: none;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-sidebar-btn:hover {
  background: var(--accent-dark);
}

.close-sidebar-btn {
  background: white;
  color: var(--primary);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  font-size: var(--font-size-xl);
  align-self: center;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-sidebar-btn:hover {
  background: rgba(255, 255, 255, 0.9);
}

/* زر فتح القائمة الجانبية */
.sidebar-toggle-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: var(--primary);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 5px;
  transition: all 0.3s ease;
}

.sidebar-toggle-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.sidebar-toggle-btn:active {
  transform: scale(0.95);
}

.sidebar-toggle-btn span {
  width: 24px;
  height: 3px;
  background: white;
  border-radius: 2px;
  display: block;
  transition: transform 0.3s ease;
}

/* زر إظهار القائمة في المحتوى */
.menu-toggle-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 100;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.menu-toggle-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.menu-toggle-btn:active {
  transform: scale(0.95);
}

/* تأثير نبض للزر */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(121, 85, 72, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(121, 85, 72, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(121, 85, 72, 0);
  }
}

.menu-toggle-btn {
  animation: pulse 2s infinite;
}

@media (min-width: 769px) {
  .menu-toggle-btn {
    display: none;
  }
}

/* محتوى الصفحة */
.manager-content {
  flex: 1;
  margin: var(--spacing-xl) auto;
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  max-width: 900px;
  width: 100%;
}

/* تنسيق لوحة المدير */
.dashboard-welcome {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.dashboard-card {
  height: 100%;
  min-height: 180px;
  transition: all var(--transition-speed) ease;
}

.dashboard-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: var(--spacing-md);
}

.dashboard-icon {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: var(--spacing-md);
}

.dashboard-card-content h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.dashboard-card-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .manager-layout {
    position: relative;
    overflow-x: hidden;
  }

  .sidebar {
    position: fixed;
    width: 100%;
    max-width: 280px;
    transform: translateX(0);
    transition: all 0.3s ease;
    z-index: 1000;
    left: 0;
    height: 100vh;
    overflow-y: auto;
  }

  .sidebar.hidden {
    transform: translateX(-100%);
    visibility: visible;
    opacity: 1;
  }

  .manager-content {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
    transition: margin-left 0.3s ease;
  }

  .manager-content.expanded {
    margin-left: 0;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}
