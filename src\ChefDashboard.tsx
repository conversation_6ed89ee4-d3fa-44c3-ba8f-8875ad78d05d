import React, { useState, useEffect, useCallback } from 'react';
import socket from './socket.ts';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import { useNotifications } from './hooks/useNotifications';
import { APP_CONFIG } from './config/app.config';
import { authenticatedGet, authenticatedPut, handleApiError } from './utils/apiHelpers';
import { getOrderFinalPrice } from './utils/orderHelpers';
import Sidebar from './components/Sidebar';
import './ChefDashboard.css';

// Define interfaces for type safety
interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  chefName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled' | 'completed';
  items: OrderItem[];
  totalPrice: number;
  preparationTime?: number;
  createdAt: string;
  updatedAt?: string;
  tableNumber?: string;
  customerName?: string;
  notes?: string;
}

interface ChefStats {
  totalOrders: number;
  pendingOrders: number;
  preparingOrders: number;
  completedOrders: number;
  averagePreparationTime: number;
}

export default function ChefDashboard() {
  // State management
  const [currentScreen, setCurrentScreen] = useState<'pending' | 'preparing' | 'completed'>('pending');
  const [pendingOrders, setPendingOrders] = useState<Order[]>([]);
  const [preparingOrders, setPreparingOrders] = useState<Order[]>([]);
  const [completedOrders, setCompletedOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [stats, setStats] = useState<ChefStats>({
    totalOrders: 0,
    pendingOrders: 0,
    preparingOrders: 0,
    completedOrders: 0,
    averagePreparationTime: 0
  });

  // Toast notifications
  const { toasts, removeToast, showSuccess, showError, showInfo } = useToast();

  // Get chef information from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const username = localStorage.getItem('username');
  const chefName = user.name || user.username || username || 'الطباخ';

  // Enhanced notifications system
  useNotifications({
    role: 'chef',
    userName: chefName,
    onOrderUpdate: () => {
      fetchOrders();
    }
  });

  // Fetch orders from server with improved error handling
  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/orders');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Filter orders by status
      const pendingOrdersData = data.filter((order: Order) => order.status === 'pending');
      const preparingOrdersData = data.filter((order: Order) =>
        order.status === 'preparing' && (!order.chefName || order.chefName === chefName)
      );
      const completedOrdersData = data.filter((order: Order) =>
        (order.status === 'ready' || order.status === 'delivered' || order.status === 'completed') &&
        (order.chefName === user._id || order.chefName === chefName || order.chefName === username)
      );
      
      // Update state
      setPendingOrders(pendingOrdersData);
      setPreparingOrders(preparingOrdersData);
      setCompletedOrders(completedOrdersData);
      
      // Update stats
      setStats({
        totalOrders: data.length,
        pendingOrders: pendingOrdersData.length,
        preparingOrders: preparingOrdersData.length,
        completedOrders: completedOrdersData.length,
        averagePreparationTime: calculateAveragePreparationTime(completedOrdersData)
      });
      
    } catch (error) {
      console.error('Error fetching orders:', error);
      showError('فشل في جلب الطلبات');
    } finally {
      setLoading(false);
    }
  }, [chefName, user._id, username, showError]);

  // Calculate average preparation time
  const calculateAveragePreparationTime = (orders: Order[]): number => {
    if (orders.length === 0) return 0;
    
    const totalTime = orders.reduce((sum, order) => {
      if (order.preparationTime) {
        return sum + order.preparationTime;
      }
      // Calculate time difference if preparationTime is not set
      const createdAt = new Date(order.createdAt);
      const updatedAt = new Date(order.updatedAt || order.createdAt);
      const timeDiff = Math.floor((updatedAt.getTime() - createdAt.getTime()) / (1000 * 60)); // in minutes
      return sum + timeDiff;
    }, 0);
    
    return Math.round(totalTime / orders.length);
  };

  // Socket.IO setup
  useEffect(() => {
    fetchOrders();

    // Register chef in Socket.IO
    socket.emit('register-user', {
      userId: user._id || 'chef-user',
      role: 'chef',
      name: chefName
    });

    socket.on('registration-confirmed', (data: any) => {
      console.log('✅ تم تسجيل الطباخ في Socket.IO:', data);
    });

    // Socket.IO real-time setup
    socket.on('new-order-notification', (data: any) => {
      console.log('🔔 طلب جديد وصل للطباخ:', data);
      showSuccess(`طلب جديد من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`);
      fetchOrders();
    });

    socket.on('order-created', (data: any) => {
      console.log('🔔 تم إنشاء طلب جديد:', data);
      showSuccess(`طلب جديد من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`);
      fetchOrders();
    });

    socket.on('order-status-update', (data: any) => {
      console.log('🔄 تحديث حالة طلب:', data);
      fetchOrders();
    });

    // Connection event listeners
    socket.on('connect', () => {
      console.log('🔌 الطباخ متصل بـ Socket.IO');
      socket.emit('register-user', {
        userId: user._id || 'chef-user',
        role: 'chef',
        name: chefName
      });
    });

    socket.on('disconnect', () => {
      console.log('❌ انقطع اتصال الطباخ من Socket.IO');
    });

    // Cleanup listeners on component unmount
    return () => {
      socket.off('new-order-notification');
      socket.off('order-created');
      socket.off('order-status-update');
      socket.off('registration-confirmed');
      socket.off('connect');
      socket.off('disconnect');
    };
  }, [chefName, showError, showSuccess, fetchOrders]);

  // Accept order and start preparation
  const handleAcceptOrder = async (orderId: string) => {
    setLoading(true);
    try {
      const orderData = {
        status: 'preparing',
        chefName: chefName,
        updatedAt: new Date().toISOString()
      };
      
      await authenticatedPut(`/api/orders/${orderId}`, orderData);
      
      // Move order from pending to preparing
      const acceptedOrder = pendingOrders.find(order => order._id === orderId);
      if (acceptedOrder) {
        setPendingOrders(orders => orders.filter(order => order._id !== orderId));
        setPreparingOrders(preparing => [...preparing, {
          ...acceptedOrder,
          status: 'preparing',
          chefName: chefName,
          updatedAt: new Date().toISOString()
        }]);
      }
      
      showSuccess(`تم قبول الطلب من الطاولة رقم ${acceptedOrder?.tableNumber || 'غير محدد'} للعميل ${acceptedOrder?.customerName || 'غير محدد'} وبدء التحضير`);

      // Send notification to waiter and manager
      if (acceptedOrder) {
        setTimeout(() => {
          socket.emit('order-status-update', {
            orderId: acceptedOrder._id,
            orderNumber: acceptedOrder.orderNumber,
            newStatus: 'preparing',
            chefName: chefName,
            waiterName: acceptedOrder.waiterName,
            tableNumber: acceptedOrder.tableNumber || 'غير محدد',
            customer: acceptedOrder.customerName || 'عميل',
            items: acceptedOrder.items,
            timestamp: new Date().toISOString()
          });
          console.log('📤 تم إرسال إشعار قبول الطلب:', acceptedOrder.orderNumber);
        }, 500);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  // Complete order preparation
  const handleCompleteOrder = async (orderId: string) => {
    setLoading(true);
    try {
      const orderData = {
        status: 'ready',
        chefName: chefName,
        updatedAt: new Date().toISOString()
      };
      
      await authenticatedPut(`/api/orders/${orderId}`, orderData);
      const orderBeingCompleted = preparingOrders.find(order => order._id === orderId);

      // Move order from preparing to completed
      setPreparingOrders(orders => orders.filter(order => order._id !== orderId));
      if (orderBeingCompleted) {
        setCompletedOrders(completed => [...completed, {
          ...orderBeingCompleted,
          status: 'ready',
          chefName: chefName,
          updatedAt: new Date().toISOString()
        }]);
      }

      showSuccess(`تم إنهاء تحضير الطلب من الطاولة رقم ${orderBeingCompleted?.tableNumber || 'غير محدد'} للعميل ${orderBeingCompleted?.customerName || 'غير محدد'} - الطلب جاهز للتقديم`);

      // Send notification to waiter and manager
      if (orderBeingCompleted) {
        setTimeout(() => {
          socket.emit('order-status-update', {
            orderId: orderBeingCompleted._id,
            orderNumber: orderBeingCompleted.orderNumber,
            newStatus: 'ready',
            chefName: chefName,
            waiterName: orderBeingCompleted.waiterName,
            tableNumber: orderBeingCompleted.tableNumber || 'غير محدد',
            customer: orderBeingCompleted.customerName || 'عميل',
            items: orderBeingCompleted.items,
            timestamp: new Date().toISOString()
          });
          console.log('📤 تم إرسال إشعار إكمال الطلب:', orderBeingCompleted.orderNumber);
        }, 500);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
    setLoading(false);
  };

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return '#f57c00';
      case 'preparing': return '#1976d2';
      case 'ready': return '#388e3c';
      default: return '#666';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز للتسليم';
      default: return status;
    }
  };

  return (
    <div className="chef-dashboard">
      {/* Sidebar */}
      <div className={`chef-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <Sidebar
          userRole="chef"
          userName={chefName}
          currentScreen={currentScreen}
          onScreenChange={(screen) => setCurrentScreen(screen as 'pending' | 'preparing' | 'completed')}
          isCollapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      </div>

      {/* Main Content */}
      <div className="chef-main-content">
        {/* Header */}
        <div className="chef-header">
          <div className="chef-header-content">
            <h1 className="chef-title">لوحة تحكم الطباخ</h1>
            <p className="chef-welcome">مرحباً {chefName}</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="chef-stats">
          <div className="chef-stat-card">
            <div className="chef-stat-number">{stats.pendingOrders}</div>
            <div className="chef-stat-label">طلبات في الانتظار</div>
          </div>
          <div className="chef-stat-card">
            <div className="chef-stat-number">{stats.preparingOrders}</div>
            <div className="chef-stat-label">قيد التحضير</div>
          </div>
          <div className="chef-stat-card">
            <div className="chef-stat-number">{stats.completedOrders}</div>
            <div className="chef-stat-label">مكتملة اليوم</div>
          </div>
          <div className="chef-stat-card">
            <div className="chef-stat-number">{stats.averagePreparationTime}</div>
            <div className="chef-stat-label">متوسط وقت التحضير (دقيقة)</div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="chef-tabs">
          <button
            onClick={() => setCurrentScreen('pending')}
            className={`chef-tab-button ${currentScreen === 'pending' ? 'active' : ''}`}
          >
            <span className="tab-count">{pendingOrders.length}</span>
            طلبات في الانتظار
          </button>
          <button
            onClick={() => setCurrentScreen('preparing')}
            className={`chef-tab-button ${currentScreen === 'preparing' ? 'active' : ''}`}
          >
            <span className="tab-count">{preparingOrders.length}</span>
            قيد التحضير
          </button>
          <button
            onClick={() => setCurrentScreen('completed')}
            className={`chef-tab-button ${currentScreen === 'completed' ? 'active' : ''}`}
          >
            <span className="tab-count">{completedOrders.length}</span>
            مكتملة
          </button>
        </div>

        {/* Screen Content */}
        {loading && (
          <div className="chef-loading">
            <div className="chef-loading-spinner"></div>
            <div className="chef-loading-text">جاري تحميل الطلبات...</div>
          </div>
        )}

        {!loading && currentScreen === 'pending' && (
          <PendingOrdersScreen
            orders={pendingOrders}
            onAccept={handleAcceptOrder}
            loading={loading}
            formatDate={formatDate}
          />
        )}
        
        {!loading && currentScreen === 'preparing' && (
          <PreparingOrdersScreen
            orders={preparingOrders}
            onComplete={handleCompleteOrder}
            loading={loading}
            formatDate={formatDate}
            getStatusColor={getStatusColor}
            getStatusText={getStatusText}
          />
        )}
        
        {!loading && currentScreen === 'completed' && (
          <CompletedOrdersScreen
            orders={completedOrders}
            loading={loading}
            formatDate={formatDate}
            getStatusColor={getStatusColor}
            getStatusText={getStatusText}
          />
        )}
      </div>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

// مكون الطلبات المعلقة
function PendingOrdersScreen({
  orders,
  onAccept,
  loading,
  formatDate
}: {
  orders: Order[];
  onAccept: (id: string) => void;
  loading: boolean;
  formatDate: (date: string) => string;
}) {
  if (orders.length === 0) {
    return (
      <div className="chef-empty-state">
        <div className="chef-empty-icon">🍽️</div>
        <h2 className="chef-empty-title">لا توجد طلبات معلقة</h2>
        <p className="chef-empty-subtitle">جميع الطلبات تم قبولها أو لا توجد طلبات جديدة</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ color: '#6d4c41', marginBottom: '1.5rem', fontSize: '1.5rem' }}>
        الطلبات في الانتظار - اختر طلب لبدء التحضير
      </h2>
      <div className="chef-orders-grid">
        {orders.map((order) => (
          <div key={order._id} className="chef-order-card pending">
            <div className="chef-order-header">
              <h3 className="chef-order-number">طلب رقم: {order.orderNumber}</h3>
              <div className="chef-order-status status-pending">جديد</div>
            </div>

            <div className="chef-order-info">
              <div className="chef-info-row">
                <span className="chef-info-label">النادل:</span>
                <span className="chef-info-value">{order.waiterName}</span>
              </div>
              <div className="chef-info-row">
                <span className="chef-info-label">الوقت:</span>
                <span className="chef-info-value">{formatDate(order.createdAt)}</span>
              </div>
              {order.tableNumber && (
                <div className="chef-info-row">
                  <span className="chef-info-label">الطاولة:</span>
                  <span className="chef-info-value">{order.tableNumber}</span>
                </div>
              )}
              {order.customerName && (
                <div className="chef-info-row">
                  <span className="chef-info-label">العميل:</span>
                  <span className="chef-info-value">{order.customerName}</span>
                </div>
              )}
            </div>

            <div className="chef-order-items">
              <h4 className="chef-items-title">الأصناف المطلوبة</h4>
              {order.items.map((item, index) => (
                <div key={index} className="chef-item">
                  <div className="chef-item-info">
                    <div className="chef-item-name">{item.name}</div>
                    <div className="chef-item-details">{item.price} ريال للقطعة</div>
                    {item.notes && (
                      <div className="chef-item-notes">ملاحظة: {item.notes}</div>
                    )}
                  </div>
                  <div className="chef-item-quantity">{item.quantity}</div>
                </div>
              ))}
            </div>

            {order.notes && (
              <div style={{
                background: '#fff3cd',
                border: '1px solid #ffeeba',
                borderRadius: '12px',
                padding: '15px',
                marginBottom: '20px'
              }}>
                <strong>ملاحظات خاصة:</strong> {order.notes}
              </div>
            )}

            <div className="chef-order-actions">
              <button
                onClick={() => onAccept(order._id)}
                disabled={loading}
                className="chef-action-button accept-button"
              >
                {loading ? 'جاري القبول...' : '✅ قبول وبدء التحضير'}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// مكون الطلبات قيد التحضير
function PreparingOrdersScreen({
  orders,
  onComplete,
  loading,
  formatDate,
  getStatusColor,
  getStatusText
}: {
  orders: Order[];
  onComplete: (id: string) => void;
  loading: boolean;
  formatDate: (date: string) => string;
  getStatusColor: (status: Order['status']) => string;
  getStatusText: (status: Order['status']) => string;
}) {
  if (orders.length === 0) {
    return (
      <div className="chef-empty-state">
        <div className="chef-empty-icon">👨‍🍳</div>
        <h2 className="chef-empty-title">لا توجد طلبات قيد التحضير</h2>
        <p className="chef-empty-subtitle">لم تقم بقبول أي طلبات بعد أو تم إنهاء جميع الطلبات</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ color: '#6d4c41', marginBottom: '1.5rem', fontSize: '1.5rem' }}>
        الطلبات قيد التحضير ({orders.length} طلب)
      </h2>
      <div className="chef-orders-grid">
        {orders.map((order) => (
          <div key={order._id} className="chef-order-card preparing">
            <div className="chef-order-header">
              <h3 className="chef-order-number">طلب رقم: {order.orderNumber}</h3>
              <div className="chef-order-status status-preparing">قيد التحضير</div>
            </div>

            <div className="chef-order-info">
              <div className="chef-info-row">
                <span className="chef-info-label">النادل:</span>
                <span className="chef-info-value">{order.waiterName}</span>
              </div>
              <div className="chef-info-row">
                <span className="chef-info-label">بدء التحضير:</span>
                <span className="chef-info-value">{order.updatedAt ? formatDate(order.updatedAt) : formatDate(order.createdAt)}</span>
              </div>
              {order.tableNumber && (
                <div className="chef-info-row">
                  <span className="chef-info-label">الطاولة:</span>
                  <span className="chef-info-value">{order.tableNumber}</span>
                </div>
              )}
              {order.customerName && (
                <div className="chef-info-row">
                  <span className="chef-info-label">العميل:</span>
                  <span className="chef-info-value">{order.customerName}</span>
                </div>
              )}
            </div>

            <div className="chef-order-items">
              <h4 className="chef-items-title">الأصناف</h4>
              {order.items.map((item, index) => (
                <div key={index} className="chef-item">
                  <div className="chef-item-info">
                    <div className="chef-item-name">{item.name}</div>
                    <div className="chef-item-details">{item.price} ريال للقطعة</div>
                    {item.notes && (
                      <div className="chef-item-notes">ملاحظة: {item.notes}</div>
                    )}
                  </div>
                  <div className="chef-item-quantity">{item.quantity}</div>
                </div>
              ))}
            </div>

            {order.notes && (
              <div style={{
                background: '#fff3cd',
                border: '1px solid #ffeeba',
                borderRadius: '12px',
                padding: '15px',
                marginBottom: '20px'
              }}>
                <strong>ملاحظات خاصة:</strong> {order.notes}
              </div>
            )}

            <div className="chef-order-actions">
              <button
                onClick={() => onComplete(order._id)}
                disabled={loading}
                className="chef-action-button ready-button"
              >
                {loading ? 'جاري الإنهاء...' : '🎯 إنهاء الطلب - مكتمل وجاهز للتسليم'}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// مكون الطلبات المكتملة
function CompletedOrdersScreen({
  orders,
  loading,
  formatDate,
  getStatusColor,
  getStatusText
}: {
  orders: Order[];
  loading: boolean;
  formatDate: (date: string) => string;
  getStatusColor: (status: Order['status']) => string;
  getStatusText: (status: Order['status']) => string;
}) {
  if (orders.length === 0) {
    return (
      <div className="chef-empty-state">
        <div className="chef-empty-icon">✅</div>
        <h2 className="chef-empty-title">لا توجد طلبات مكتملة</h2>
        <p className="chef-empty-subtitle">لم تقم بإكمال أي طلبات بعد</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ color: '#6d4c41', marginBottom: '1.5rem', fontSize: '1.5rem' }}>
        الطلبات المكتملة ({orders.length} طلب)
      </h2>
      <div className="chef-orders-grid">
        {orders.map((order) => (
          <div key={order._id} className="chef-order-card completed">
            <div className="chef-order-header">
              <h3 className="chef-order-number">طلب رقم: {order.orderNumber}</h3>
              <div className="chef-order-status status-ready">مكتمل</div>
            </div>

            <div className="chef-order-info">
              <div className="chef-info-row">
                <span className="chef-info-label">النادل:</span>
                <span className="chef-info-value">{order.waiterName}</span>
              </div>
              <div className="chef-info-row">
                <span className="chef-info-label">تم الإنهاء:</span>
                <span className="chef-info-value">{order.updatedAt ? formatDate(order.updatedAt) : formatDate(order.createdAt)}</span>
              </div>
              {order.tableNumber && (
                <div className="chef-info-row">
                  <span className="chef-info-label">الطاولة:</span>
                  <span className="chef-info-value">{order.tableNumber}</span>
                </div>
              )}
              {order.customerName && (
                <div className="chef-info-row">
                  <span className="chef-info-label">العميل:</span>
                  <span className="chef-info-value">{order.customerName}</span>
                </div>
              )}
            </div>

            <div className="chef-order-items">
              <h4 className="chef-items-title">الأصناف</h4>
              {order.items.map((item, index) => (
                <div key={index} className="chef-item">
                  <div className="chef-item-info">
                    <div className="chef-item-name">{item.name}</div>
                    <div className="chef-item-details">{item.price} ريال للقطعة</div>
                  </div>
                  <div className="chef-item-quantity">{item.quantity}</div>
                </div>
              ))}
            </div>

            <div style={{
              width: '100%',
              padding: '15px',
              background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
              color: 'white',
              borderRadius: '12px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              textAlign: 'center',
              marginTop: '20px'
            }}>
              ✅ تم الإنهاء - جاهز للتسليم
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
