import { useState, useEffect, useCallback } from 'react';
import { authenticatedGet, authenticatedPut, handleApiError } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import type { Order } from './types/Order';
import { getOrderTotal, formatOrderPrice } from './types/Order';
import socket from './socket';
import './ChefDashboard.css';

interface ChefDashboardProps {
  user: any;
  onLogout: () => void;
}

export default function ChefDashboard({ user, onLogout }: ChefDashboardProps) {
  const [currentScreen, setCurrentScreen] = useState<'pending' | 'preparing' | 'completed'>('pending');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  const { showSuccess, showError } = useToast();
  const chefName = user?.username || user?.name || 'طباخ';

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
      if (window.innerWidth > 768) {
        setSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const openSidebar = () => setSidebarOpen(true);
  const closeSidebar = () => setSidebarOpen(false);

  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const data = await authenticatedGet('/api/orders');
      setOrders(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const getFilteredOrders = (status: string) => {
    return orders.filter(order => {
      if (status === 'pending') {
        return order.status === 'pending';
      } else if (status === 'preparing') {
        return order.status === 'preparing' && order.chefName === chefName;
      } else if (status === 'completed') {
        return (order.status === 'ready' || order.status === 'delivered' || order.status === 'completed') &&
               order.chefName === chefName;
      }
      return false;
    });
  };

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'preparing',
        chefName: chefName
      });

      const acceptedOrder = orders.find(order => order._id === orderId);
      showSuccess(`تم قبول الطلب من الطاولة رقم ${acceptedOrder?.tableNumber || 'غير محدد'} وبدء التحضير`);

      if (acceptedOrder) {
        socket.emit('order-status-update', {
          orderId: acceptedOrder._id,
          orderNumber: acceptedOrder.orderNumber,
          newStatus: 'preparing',
          chefName: chefName,
          waiterName: acceptedOrder.waiterName,
          tableNumber: acceptedOrder.tableNumber || 'غير محدد',
          customer: acceptedOrder.customerName || 'عميل',
          items: acceptedOrder.items,
          timestamp: new Date().toISOString()
        });
      }

      fetchOrders();
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  const handleCompleteOrder = async (orderId: string) => {
    try {
      await authenticatedPut(`/api/orders/${orderId}`, {
        status: 'ready'
      });

      const completedOrder = orders.find(order => order._id === orderId);
      showSuccess(`تم إنهاء تحضير الطلب من الطاولة رقم ${completedOrder?.tableNumber || 'غير محدد'} - الطلب جاهز للتقديم`);

      if (completedOrder) {
        socket.emit('order-status-update', {
          orderId: completedOrder._id,
          orderNumber: completedOrder.orderNumber,
          newStatus: 'ready',
          chefName: chefName,
          waiterName: completedOrder.waiterName,
          tableNumber: completedOrder.tableNumber || 'غير محدد',
          customer: completedOrder.customerName || 'عميل',
          items: completedOrder.items,
          timestamp: new Date().toISOString()
        });
      }

      fetchOrders();
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  useEffect(() => {
    fetchOrders();

    socket.emit('register-user', {
      userId: user._id || 'chef-user',
      role: 'chef',
      name: chefName
    });

    socket.on('registration-confirmed', (data: any) => {
      console.log('✅ تم تسجيل الطباخ في Socket.IO:', data);
    });

    socket.on('new-order-notification', (data: any) => {
      console.log('🔔 طلب جديد وصل للطباخ:', data);
      showSuccess(`طلب جديد من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`);
      fetchOrders();
    });

    socket.on('order-created', (data: any) => {
      console.log('🔔 تم إنشاء طلب جديد:', data);
      showSuccess(`طلب جديد من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`);
      fetchOrders();
    });

    socket.on('order-status-update', (data: any) => {
      console.log('🔄 تحديث حالة طلب:', data);
      fetchOrders();
    });

    socket.on('connect', () => {
      console.log('🔌 الطباخ متصل بـ Socket.IO');
      socket.emit('register-user', {
        userId: user._id || 'chef-user',
        role: 'chef',
        name: chefName
      });
    });

    socket.on('disconnect', () => {
      console.log('❌ انقطع اتصال الطباخ من Socket.IO');
    });

    return () => {
      socket.off('registration-confirmed');
      socket.off('new-order-notification');
      socket.off('order-created');
      socket.off('order-status-update');
      socket.off('connect');
      socket.off('disconnect');
    };
  }, [user, chefName, showSuccess, fetchOrders]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fa-clock';
      case 'preparing': return 'fa-fire';
      case 'ready': return 'fa-check-circle';
      case 'delivered': return 'fa-truck';
      default: return 'fa-question-circle';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      default: return 'غير محدد';
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    localStorage.removeItem('userId');
    onLogout();
  };

  const currentOrders = getFilteredOrders(currentScreen);

  const renderMainContent = () => {
    return (
      <div className="content-container">
        <div className="screen-header">
          <h1 className="screen-title">
            <i className={`fas ${getStatusIcon(currentScreen)}`}></i>
            {currentScreen === 'pending' && 'طلبات قيد الانتظار'}
            {currentScreen === 'preparing' && 'طلبات قيد التحضير'}
            {currentScreen === 'completed' && 'طلبات مكتملة'}
          </h1>
          <p className="screen-subtitle">
            {currentScreen === 'pending' && 'طلبات جديدة تحتاج إلى قبول'}
            {currentScreen === 'preparing' && 'طلبات تحت التحضير'}
            {currentScreen === 'completed' && 'طلبات تم إنجازها'}
          </p>
        </div>

        <div className="orders-stats">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-receipt"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number">{currentOrders.length}</div>
              <div className="stat-label">
                {currentScreen === 'pending' && 'طلبات منتظرة'}
                {currentScreen === 'preparing' && 'قيد التحضير'}
                {currentScreen === 'completed' && 'مكتملة'}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number">
                {currentOrders.reduce((sum, order) => sum + getOrderTotal(order), 0).toFixed(2)}
              </div>
              <div className="stat-label">إجمالي القيمة</div>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner">
              <i className="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل الطلبات...</p>
          </div>
        ) : currentOrders.length === 0 ? (
          <div className="empty-state">
            <i className={`fas ${getStatusIcon(currentScreen)} empty-icon`}></i>
            <h3>
              {currentScreen === 'pending' && 'لا توجد طلبات منتظرة'}
              {currentScreen === 'preparing' && 'لا توجد طلبات قيد التحضير'}
              {currentScreen === 'completed' && 'لا توجد طلبات مكتملة'}
            </h3>
            <p>
              {currentScreen === 'pending' && 'جميع الطلبات تم قبولها أو لا توجد طلبات جديدة'}
              {currentScreen === 'preparing' && 'لا توجد طلبات تحت التحضير حالياً'}
              {currentScreen === 'completed' && 'لم تكمل أي طلبات بعد'}
            </p>
          </div>
        ) : (
          <div className="orders-grid">
            {currentOrders.map(order => (
              <div key={order._id} className={`order-card ${order.status}`}>
                <div className="order-header">
                  <div className="order-number">
                    <i className="fas fa-receipt"></i>
                    طلب #{order.orderNumber}
                  </div>
                  <div className={`order-status ${order.status}`}>
                    <i className={`fas ${getStatusIcon(order.status)}`}></i>
                    {getStatusText(order.status)}
                  </div>
                </div>

                <div className="order-info">
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-table"></i>
                      الطاولة:
                    </span>
                    <span className="value">{order.tableNumber || 'غير محدد'}</span>
                  </div>

                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user"></i>
                      العميل:
                    </span>
                    <span className="value">{order.customerName || 'غير محدد'}</span>
                  </div>

                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user-tie"></i>
                      النادل:
                    </span>
                    <span className="value">{order.waiterName}</span>
                  </div>

                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-clock"></i>
                      الوقت:
                    </span>
                    <span className="value">{new Date(order.createdAt).toLocaleString('ar-EG')}</span>
                  </div>

                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-money-bill-wave"></i>
                      المبلغ:
                    </span>
                    <span className="value">{formatOrderPrice(order, 'ج.م')}</span>
                  </div>
                </div>

                <div className="order-items">
                  <h4>
                    <i className="fas fa-list"></i>
                    الأصناف ({order.items.length})
                  </h4>
                  <div className="items-list">
                    {order.items.slice(0, 3).map((item, index) => (
                      <div key={index} className="item-summary">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">×{item.quantity}</span>
                      </div>
                    ))}
                    {order.items.length > 3 && (
                      <div className="more-items">
                        +{order.items.length - 3} أصناف أخرى
                      </div>
                    )}
                  </div>
                </div>

                <div className="order-actions">
                  <button
                    className="btn-details"
                    onClick={() => {
                      setSelectedOrderDetails(order);
                      setShowOrderDetailsModal(true);
                    }}
                  >
                    <i className="fas fa-info-circle"></i>
                    التفاصيل
                  </button>

                  {currentScreen === 'pending' && (
                    <button
                      className="btn-accept"
                      onClick={() => handleAcceptOrder(order._id)}
                    >
                      <i className="fas fa-check"></i>
                      قبول الطلب
                    </button>
                  )}

                  {currentScreen === 'preparing' && (
                    <button
                      className="btn-complete"
                      onClick={() => handleCompleteOrder(order._id)}
                    >
                      <i className="fas fa-check-circle"></i>
                      إنهاء التحضير
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="chef-dashboard">
      {isMobile && !sidebarOpen && (
        <button className="mobile-menu-toggle" onClick={openSidebar}>
          <i className="fas fa-bars"></i>
        </button>
      )}

      <>
        {isMobile && sidebarOpen && (
          <div className="sidebar-overlay active" onClick={closeSidebar}></div>
        )}
        <div className={`dashboard-sidebar${sidebarOpen ? ' visible' : ''}${isMobile && !sidebarOpen ? ' hidden' : ''}`}
          style={isMobile ? { right: sidebarOpen ? 0 : '-100vw', transition: 'right 0.3s' } : {}}>

          <div className="sidebar-header">
            <div className="sidebar-logo">
              <i className="fas fa-utensils"></i>
              <span>مطبخ ديشا</span>
            </div>
            {isMobile && (
              <button className="sidebar-close-btn" onClick={closeSidebar}>
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>

          <nav className="sidebar-nav">
            <ul className="nav-menu">
              <li className="nav-item">
                <button
                  className={`nav-link ${currentScreen === 'pending' ? 'active' : ''}`}
                  onClick={() => { setCurrentScreen('pending'); if(isMobile) closeSidebar(); }}
                >
                  <i className="fas fa-clock nav-icon"></i>
                  <span className="nav-text">قيد الانتظار ({getFilteredOrders('pending').length})</span>
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${currentScreen === 'preparing' ? 'active' : ''}`}
                  onClick={() => { setCurrentScreen('preparing'); if(isMobile) closeSidebar(); }}
                >
                  <i className="fas fa-fire nav-icon"></i>
                  <span className="nav-text">قيد التحضير ({getFilteredOrders('preparing').length})</span>
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${currentScreen === 'completed' ? 'active' : ''}`}
                  onClick={() => { setCurrentScreen('completed'); if(isMobile) closeSidebar(); }}
                >
                  <i className="fas fa-check-circle nav-icon"></i>
                  <span className="nav-text">مكتملة ({getFilteredOrders('completed').length})</span>
                </button>
              </li>
            </ul>
          </nav>

          <div className="sidebar-stats">
            <div className="stats-title">
              <i className="fas fa-chart-line"></i>
              إحصائيات اليوم
            </div>
            <div className="stat-item">
              <div className="stat-icon">
                <i className="fas fa-clock"></i>
              </div>
              <div className="stat-details">
                <span className="stat-value">{getFilteredOrders('pending').length}</span>
                <span className="stat-label">طلبات منتظرة</span>
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-icon">
                <i className="fas fa-fire"></i>
              </div>
              <div className="stat-details">
                <span className="stat-value">{getFilteredOrders('preparing').length}</span>
                <span className="stat-label">قيد التحضير</span>
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-icon">
                <i className="fas fa-check-circle"></i>
              </div>
              <div className="stat-details">
                <span className="stat-value">{getFilteredOrders('completed').length}</span>
                <span className="stat-label">مكتملة</span>
              </div>
            </div>
          </div>

          <div className="sidebar-footer">
            <div className="chef-info">
              <div className="chef-avatar">
                <i className="fas fa-user-circle"></i>
              </div>
              <div className="chef-details">
                <div className="chef-name">{chefName}</div>
                <div className="chef-role">طباخ</div>
              </div>
            </div>
            <button className="logout-btn" onClick={handleLogout}>
              <i className="fas fa-sign-out-alt"></i>
              <span>تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </>

      <main className="dashboard-main">
        <div className="content-card">
          {renderMainContent()}
        </div>

        {showOrderDetailsModal && selectedOrderDetails && (
          <div className="modal-overlay" onClick={() => setShowOrderDetailsModal(false)}>
            <div className="modal-content order-details-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-receipt"></i>
                  تفاصيل الطلب #{selectedOrderDetails.orderNumber}
                </h2>
                <button
                  className="modal-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>

              <div className="modal-body">
                <div className="order-info-section">
                  <h3>
                    <i className="fas fa-info-circle"></i>
                    معلومات الطلب
                  </h3>
                  <div className="order-info-grid">
                    <div className="info-item">
                      <span className="label">الطاولة:</span>
                      <span className="value">{selectedOrderDetails.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">العميل:</span>
                      <span className="value">{selectedOrderDetails.customerName || 'غير محدد'}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedOrderDetails.waiterName}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span className={`status-badge ${selectedOrderDetails.status}`}>
                        <i className={`fas ${getStatusIcon(selectedOrderDetails.status)}`}></i>
                        {getStatusText(selectedOrderDetails.status)}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">وقت الطلب:</span>
                      <span className="value">{new Date(selectedOrderDetails.createdAt).toLocaleString('ar-EG')}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">المبلغ الإجمالي:</span>
                      <span className="value total-amount">{formatOrderPrice(selectedOrderDetails, 'ج.م')}</span>
                    </div>
                  </div>
                </div>

                <div className="order-items-section">
                  <h3>
                    <i className="fas fa-list"></i>
                    الأصناف المطلوبة ({selectedOrderDetails.items.length})
                  </h3>
                  <div className="items-list">
                    {selectedOrderDetails.items.map((item, index) => (
                      <div key={index} className="item-card">
                        <div className="item-info">
                          <div className="item-name">{item.name}</div>
                          {item.notes && (
                            <div className="item-notes">
                              <i className="fas fa-sticky-note"></i>
                              ملاحظة: {item.notes}
                            </div>
                          )}
                        </div>
                        <div className="item-details">
                          <div className="item-quantity">
                            <i className="fas fa-times"></i>
                            {item.quantity}
                          </div>
                          <div className="item-price">{item.price} ج.م</div>
                          <div className="item-total">{(item.price * item.quantity).toFixed(2)} ج.م</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="modal-footer">
                {selectedOrderDetails.status === 'pending' && (
                  <button
                    className="btn-accept"
                    onClick={() => {
                      handleAcceptOrder(selectedOrderDetails._id);
                      setShowOrderDetailsModal(false);
                    }}
                  >
                    <i className="fas fa-check"></i>
                    قبول الطلب
                  </button>
                )}

                {selectedOrderDetails.status === 'preparing' && selectedOrderDetails.chefName === chefName && (
                  <button
                    className="btn-complete"
                    onClick={() => {
                      handleCompleteOrder(selectedOrderDetails._id);
                      setShowOrderDetailsModal(false);
                    }}
                  >
                    <i className="fas fa-check-circle"></i>
                    إنهاء التحضير
                  </button>
                )}

                <button
                  className="btn-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}