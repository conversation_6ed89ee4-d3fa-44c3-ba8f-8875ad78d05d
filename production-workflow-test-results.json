{"passed": 8, "failed": 0, "tests": [{"name": "Production Backend Health", "status": "PASSED", "duration": 1237}, {"name": "Production Frontend Health", "status": "PASSED", "duration": 1189}, {"name": "User Authentication (All Roles)", "status": "PASSED", "duration": 2910}, {"name": "Products API", "status": "PASSED", "duration": 272}, {"name": "Order Validation", "status": "PASSED", "duration": 158}, {"name": "Order Creation", "status": "PASSED", "duration": 1392}, {"name": "Chef Order Management", "status": "PASSED", "duration": 1741}, {"name": "Manager Access", "status": "PASSED", "duration": 846}], "timing": {"Production Backend Health": 1237, "Production Frontend Health": 1189, "User Authentication (All Roles)": 2910, "Products API": 272, "Order Validation": 158, "Order Creation": 1392, "Chef Order Management": 1741, "Manager Access": 846}}